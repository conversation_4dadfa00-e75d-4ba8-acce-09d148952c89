<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "3CF301DA-6D23-425D-8B47-0BA5798E8B96"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D24A7F03-9382-4053-B7D2-5366D9A014CD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Home/ProductListVC.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "190"
            endingLineNumber = "190"
            landmarkName = "addWishlist(wishlist:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E6BC973C-D316-44F4-9834-0A9837B45BD6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Home/HomeVM.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "182"
            endingLineNumber = "182"
            landmarkName = "addwishlist(wishlist:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "89A3257E-8B46-4FAB-9089-2E609C28C431"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Home/Detailpage/ProductdetailVC.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "142"
            endingLineNumber = "142"
            landmarkName = "addWishlist(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F02764C1-BAD6-4797-8301-317D69BDED70"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Home/Detailpage/ProductdetailVC.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "247"
            endingLineNumber = "247"
            landmarkName = "addWishlist(wishlist:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C6D21414-1E57-4F26-9150-A8D99BE77D2A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Module/Cart/Payment/ViewModel/PaymentViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "251"
            endingLineNumber = "251"
            landmarkName = "onPaymentCompletion(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E11D15C9-572B-49A6-9204-D86C8B6D71F1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Module/Cart/Payment/ViewModel/PaymentViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "253"
            endingLineNumber = "253"
            landmarkName = "onPaymentCompletion(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D616715B-327C-4040-B150-38138641DE37"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Module/Cart/Payment/ViewModel/PaymentViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "256"
            endingLineNumber = "256"
            landmarkName = "onPaymentCompletion(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "747B7BA6-1B44-4F8A-9DCD-C33C36CC4398"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Module/Cart/Payment/ViewModel/WebView/WebContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "125"
            endingLineNumber = "125"
            landmarkName = "webView(_:decidePolicyFor:decisionHandler:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "54BBD66B-7633-4650-98A5-608BC4D73219"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Module/Cart/Payment/ViewModel/WebView/WebContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "121"
            endingLineNumber = "121"
            landmarkName = "webView(_:decidePolicyFor:decisionHandler:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "48087F4E-B744-4536-94F2-D10543D86AE3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "BabyHouse/Global/Helper/Network_manager/LightNetwork/Network.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "143"
            endingLineNumber = "143"
            landmarkName = "decode()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>

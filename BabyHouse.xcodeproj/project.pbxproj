// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		0314B71C28A2661B001E050E /* TrackTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0314B71A28A2661B001E050E /* TrackTableViewCell.swift */; };
		0314B71D28A2661B001E050E /* TrackTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0314B71B28A2661B001E050E /* TrackTableViewCell.xib */; };
		0314B71F28A2703E001E050E /* PaymentsuccessVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0314B71E28A2703E001E050E /* PaymentsuccessVC.swift */; };
		0327024B288287040010AEF8 /* Metropolis-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0327024A288287040010AEF8 /* Metropolis-Medium.otf */; };
		0327024D288287130010AEF8 /* Metropolis-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0327024C288287130010AEF8 /* Metropolis-Regular.otf */; };
		0327024F2882871F0010AEF8 /* Metropolis-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0327024E2882871F0010AEF8 /* Metropolis-Bold.otf */; };
		03270251288287290010AEF8 /* Metropolis-Black.otf in Resources */ = {isa = PBXBuildFile; fileRef = 03270250288287290010AEF8 /* Metropolis-Black.otf */; };
		03270253288287340010AEF8 /* Metropolis-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 03270252288287340010AEF8 /* Metropolis-Light.otf */; };
		0332651F2886990900927B09 /* TagCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0332651D2886990900927B09 /* TagCollectionViewCell.swift */; };
		033265202886990900927B09 /* TagCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0332651E2886990900927B09 /* TagCollectionViewCell.xib */; };
		033265222886C11A00927B09 /* MapVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 033265212886C11A00927B09 /* MapVC.swift */; };
		033333F12850708500D95000 /* ChoosecurrencyVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 033333F02850708500D95000 /* ChoosecurrencyVC.swift */; };
		0358F0412887CE9A00A8CF80 /* AreaVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0358F0402887CE9A00A8CF80 /* AreaVC.swift */; };
		0358F0442887CEDC00A8CF80 /* AreaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0358F0422887CEDC00A8CF80 /* AreaTableViewCell.swift */; };
		0358F0452887CEDC00A8CF80 /* AreaTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0358F0432887CEDC00A8CF80 /* AreaTableViewCell.xib */; };
		035FD1F0287AAF3C00910980 /* BlogVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 035FD1EF287AAF3C00910980 /* BlogVC.swift */; };
		035FD1F3287AB0A600910980 /* BlogTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 035FD1F1287AB0A600910980 /* BlogTableViewCell.swift */; };
		035FD1F4287AB0A600910980 /* BlogTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 035FD1F2287AB0A600910980 /* BlogTableViewCell.xib */; };
		035FD1F6287AB55900910980 /* ProductListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 035FD1F5287AB55900910980 /* ProductListVC.swift */; };
		036A2BBC28A21F5F0085AE87 /* SearchVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 036A2BBB28A21F5F0085AE87 /* SearchVC.swift */; };
		036A2BC128A21F750085AE87 /* SearchModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 036A2BC028A21F750085AE87 /* SearchModel.swift */; };
		036A2BC828A2211F0085AE87 /* ProductsearchTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 036A2BC628A2211F0085AE87 /* ProductsearchTableViewCell.swift */; };
		036A2BC928A2211F0085AE87 /* ProductsearchTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 036A2BC728A2211F0085AE87 /* ProductsearchTableViewCell.xib */; };
		036A2BCB28A222020085AE87 /* Search.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 036A2BCA28A222020085AE87 /* Search.storyboard */; };
		036D7B4F285773A50055F427 /* ChoosecountryVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 036D7B4E285773A50055F427 /* ChoosecountryVC.swift */; };
		03950D3D284F2E0700D7C19E /* CattitleCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03950D3B284F2E0700D7C19E /* CattitleCollectionViewCell.swift */; };
		03950D3E284F2E0700D7C19E /* CattitleCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 03950D3C284F2E0700D7C19E /* CattitleCollectionViewCell.xib */; };
		03A1E4F728479EEE007B955A /* CategoryprodutsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03A1E4F628479EEE007B955A /* CategoryprodutsVC.swift */; };
		07E418F3B6AB1EAC37DAE6E7 /* Pods_BabyHouse_BabyHouseUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95AA07DFA3388DC425A0AB27 /* Pods_BabyHouse_BabyHouseUITests.framework */; };
		182521722AEBC5C000402770 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 182521712AEBC5C000402770 /* FirebaseMessaging */; };
		182521742AEC066800402770 /* PromoListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 182521732AEC066800402770 /* PromoListView.swift */; };
		1825217A2AEC131600402770 /* OtpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 182521792AEC131600402770 /* OtpView.swift */; };
		1825217C2AEC134500402770 /* OtpViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1825217B2AEC134500402770 /* OtpViewModel.swift */; };
		1849E13D2AB8491300751BE3 /* Siren in Frameworks */ = {isa = PBXBuildFile; productRef = 1849E13C2AB8491300751BE3 /* Siren */; };
		1865DB792C7A351B00C7C9D7 /* InfiniteCarousel in Frameworks */ = {isa = PBXBuildFile; productRef = 1865DB782C7A351B00C7C9D7 /* InfiniteCarousel */; };
		1865DB7B2C7A3C8900C7C9D7 /* CarouselView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1865DB7A2C7A3C8900C7C9D7 /* CarouselView.swift */; };
		1865DB812C7A4CD600C7C9D7 /* SwiftUIInfiniteCarousel in Frameworks */ = {isa = PBXBuildFile; productRef = 1865DB802C7A4CD600C7C9D7 /* SwiftUIInfiniteCarousel */; };
		1871A1922AA0DC2B00978E37 /* Notification.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1871A1912AA0DC2B00978E37 /* Notification.swift */; };
		1871A1952AA0E95D00978E37 /* ProductDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1871A1942AA0E95D00978E37 /* ProductDetailView.swift */; };
		1871A1972AA0F82200978E37 /* ViewWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1871A1962AA0F82200978E37 /* ViewWrapper.swift */; };
		1882ECCD2AAF6FF700186C87 /* PaymentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1882ECCC2AAF6FF700186C87 /* PaymentView.swift */; };
		1882ECCF2AAF969A00186C87 /* PaymentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1882ECCE2AAF969A00186C87 /* PaymentModel.swift */; };
		1882ECD32AAF9E9300186C87 /* PaymentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1882ECD22AAF9E9300186C87 /* PaymentViewModel.swift */; };
		18A3137D2E5081DF0036A202 /* AuthenticationLogger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A3137C2E5081DF0036A202 /* AuthenticationLogger.swift */; };
		18A3137E2E5081DF0036A202 /* APITestingGuide.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A3137B2E5081DF0036A202 /* APITestingGuide.swift */; };
		18AD6D922ACC07AB00A2903B /* KeychainItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AD6D912ACC07AB00A2903B /* KeychainItem.swift */; };
		18AD6D982ACC13F100A2903B /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 18AD6D972ACC13F100A2903B /* GoogleSignIn */; };
		18AD6D9A2ACC13F100A2903B /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 18AD6D992ACC13F100A2903B /* GoogleSignInSwift */; };
		18AD6D9D2ACC179500A2903B /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = 18AD6D9C2ACC179500A2903B /* FirebaseAuth */; };
		18B4C4F52AB0B1210062735F /* LoyaltyCashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B4C4F42AB0B1210062735F /* LoyaltyCashView.swift */; };
		18B4C4F82AB0B2A10062735F /* InviteFriendsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B4C4F62AB0B1A50062735F /* InviteFriendsView.swift */; };
		18B4C4F92AB0B2AB0062735F /* WalletView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B4C4F72AB0B1A80062735F /* WalletView.swift */; };
		18B4C4FC2AB0B91D0062735F /* BadgeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B4C4FB2AB0B91D0062735F /* BadgeView.swift */; };
		18B4C4FF2AB0C5C00062735F /* NavigationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B4C4FE2AB0C5C00062735F /* NavigationView.swift */; };
		18B6968B2AB1A67900879BA6 /* Font.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B6968A2AB1A67900879BA6 /* Font.swift */; };
		18B6968D2AB1A71F00879BA6 /* Metropolis-SemiBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 18B6968C2AB1A71F00879BA6 /* Metropolis-SemiBold.otf */; };
		18B696902AB1C45900879BA6 /* OrderListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B6968F2AB1C45900879BA6 /* OrderListView.swift */; };
		18B696922AB1C48E00879BA6 /* OrderListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B696912AB1C48E00879BA6 /* OrderListViewModel.swift */; };
		18B696942AB1C4B300879BA6 /* OrderListModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B696932AB1C4B300879BA6 /* OrderListModel.swift */; };
		18B696962AB1C53900879BA6 /* MyOrderListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B696952AB1C53900879BA6 /* MyOrderListVC.swift */; };
		18B696982AB1D33900879BA6 /* WalletViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B696972AB1D33900879BA6 /* WalletViewModel.swift */; };
		18B6969A2AB1D34200879BA6 /* WalletModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B696992AB1D34200879BA6 /* WalletModel.swift */; };
		18B6969C2AB1D35C00879BA6 /* LoyaltyCashViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B6969B2AB1D35C00879BA6 /* LoyaltyCashViewModel.swift */; };
		18B6969E2AB1D36500879BA6 /* LoyaltyCashModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B6969D2AB1D36500879BA6 /* LoyaltyCashModel.swift */; };
		18BA51492AAAFA88002FEC90 /* WrappingHStack in Frameworks */ = {isa = PBXBuildFile; productRef = 18BA51482AAAFA88002FEC90 /* WrappingHStack */; };
		18BA514A2AAB3763002FEC90 /* login.json in Resources */ = {isa = PBXBuildFile; fileRef = 033301CC288144430037A7BD /* login.json */; };
		18BA514C2AAB378F002FEC90 /* custom_loader.json in Resources */ = {isa = PBXBuildFile; fileRef = 18BA514B2AAB378F002FEC90 /* custom_loader.json */; };
		18BA51502AAB3FA2002FEC90 /* splash_screen.json in Resources */ = {isa = PBXBuildFile; fileRef = 18BA514F2AAB3FA2002FEC90 /* splash_screen.json */; };
		18BA51522AAB5ECB002FEC90 /* SendEnquiryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18BA51512AAB5ECB002FEC90 /* SendEnquiryView.swift */; };
		18BA51542AAB643D002FEC90 /* HomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18BA51532AAB643D002FEC90 /* HomeViewModel.swift */; };
		18BFCACA2AC2E9DB00B00222 /* WebContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18BFCAC92AC2E9DB00B00222 /* WebContentView.swift */; };
		18CBE8802AB5C79F00B5BF57 /* ShareEarnModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CBE87F2AB5C79F00B5BF57 /* ShareEarnModel.swift */; };
		18CBE8822AB5C82800B5BF57 /* InviteFriendsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CBE8812AB5C82800B5BF57 /* InviteFriendsViewModel.swift */; };
		18CC10D12AA31D9C00C2C1E3 /* ProductDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10D02AA31D9C00C2C1E3 /* ProductDetailViewModel.swift */; };
		18CC10D52AA321B700C2C1E3 /* SuperView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10D42AA321B700C2C1E3 /* SuperView.swift */; };
		18CC10D82AA3221100C2C1E3 /* SuperModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10D72AA3221100C2C1E3 /* SuperModel.swift */; };
		18CC10DD2AA322AF00C2C1E3 /* AlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10DC2AA322AF00C2C1E3 /* AlertView.swift */; };
		18CC10E02AA322F200C2C1E3 /* Utilities.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10DF2AA322F200C2C1E3 /* Utilities.swift */; };
		18CC10E42AA3238C00C2C1E3 /* ActivityLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10E32AA3238C00C2C1E3 /* ActivityLoader.swift */; };
		18CC10E72AA323D800C2C1E3 /* LottieView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10E62AA323D800C2C1E3 /* LottieView.swift */; };
		18CC10ED2AA3331F00C2C1E3 /* CustomDotsIndexView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10EC2AA3331F00C2C1E3 /* CustomDotsIndexView.swift */; };
		18CC10F02AA3389C00C2C1E3 /* ViewportHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10EF2AA3389C00C2C1E3 /* ViewportHelper.swift */; };
		18CC10F32AA33B7C00C2C1E3 /* BabyHouseImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10F22AA33B7C00C2C1E3 /* BabyHouseImageView.swift */; };
		18CC10F62AA33BA800C2C1E3 /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = 18CC10F52AA33BA800C2C1E3 /* SDWebImageSwiftUI */; };
		18CC10F82AA33CD400C2C1E3 /* View.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10F72AA33CD400C2C1E3 /* View.swift */; };
		18CC10FC2AA35AD500C2C1E3 /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10FB2AA35AD500C2C1E3 /* AppState.swift */; };
		18CC10FE2AA3672C00C2C1E3 /* Color.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CC10FD2AA3672C00C2C1E3 /* Color.swift */; };
		18D09E552ACAC02900D3511B /* CollectionViewFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18D09E542ACAC02900D3511B /* CollectionViewFlowLayout.swift */; };
		18D2F5152AC411A300F6BECD /* SwiftyJSON in Frameworks */ = {isa = PBXBuildFile; productRef = 18D2F5142AC411A300F6BECD /* SwiftyJSON */; };
		18D2F5182AC43F0300F6BECD /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18D2F5172AC43F0300F6BECD /* LoadingView.swift */; };
		18D2F51A2AC43F4000F6BECD /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18D2F5192AC43F4000F6BECD /* WebView.swift */; };
		18D2F51E2AC43FB700F6BECD /* WebContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18D2F51D2AC43FB700F6BECD /* WebContainer.swift */; };
		18D8E65F2AA5D43900189C95 /* RichText in Frameworks */ = {isa = PBXBuildFile; productRef = 18D8E65E2AA5D43900189C95 /* RichText */; };
		18DC2ED82AEFE52E006E3D5D /* ResetPasswordVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DC2ED72AEFE52E006E3D5D /* ResetPasswordVC.swift */; };
		18E121CB2AECF9ED007D3C13 /* ForgetPasswordVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E121CA2AECF9ED007D3C13 /* ForgetPasswordVC.swift */; };
		18E121CD2AED1EEB007D3C13 /* VerifyOtpVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E121CC2AED1EEB007D3C13 /* VerifyOtpVC.swift */; };
		18E980F72BB186BA00ACF8FD /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 18E980F62BB186BA00ACF8FD /* GoogleService-Info.plist */; };
		18EFC2E82AB9BE0B0029010D /* GovernorateVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EFC2E72AB9BE0B0029010D /* GovernorateVC.swift */; };
		1A852A0128029B8A0074191B /* SignupVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A852A0028029B890074191B /* SignupVM.swift */; };
		1A8A17D2285F8A4B0089CE04 /* MyOrderVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A8A17D1285F8A4B0089CE04 /* MyOrderVM.swift */; };
		1A8A17D4285F8B260089CE04 /* OrderRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A8A17D3285F8B260089CE04 /* OrderRequest.swift */; };
		1A8A17D6285F9B1D0089CE04 /* AddressVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A8A17D5285F9B1D0089CE04 /* AddressVM.swift */; };
		1A8A17D8285F9B620089CE04 /* AddressRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A8A17D7285F9B620089CE04 /* AddressRequest.swift */; };
		1A9177B227F3754D003EA672 /* TitleTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9177AB27F3754D003EA672 /* TitleTableViewCell.swift */; };
		1A9177B327F3754D003EA672 /* TitleTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 1A9177AC27F3754D003EA672 /* TitleTableViewCell.xib */; };
		1A9177B427F3754D003EA672 /* CategoryTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 1A9177AD27F3754D003EA672 /* CategoryTableViewCell.xib */; };
		1A9177B527F3754D003EA672 /* Category.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A9177AE27F3754D003EA672 /* Category.storyboard */; };
		1A9177B627F3754D003EA672 /* CategoryVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9177AF27F3754D003EA672 /* CategoryVM.swift */; };
		1A9177B727F3754D003EA672 /* CategoryVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9177B027F3754D003EA672 /* CategoryVC.swift */; };
		1A9177B827F3754D003EA672 /* CategoryTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9177B127F3754D003EA672 /* CategoryTableViewCell.swift */; };
		1A9177BB27F37F55003EA672 /* Login.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A9177BA27F37F55003EA672 /* Login.storyboard */; };
		1A9177BD27F38917003EA672 /* LoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9177BC27F38917003EA672 /* LoginVC.swift */; };
		1A9177C027F38B16003EA672 /* Payment.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A9177BF27F38B16003EA672 /* Payment.storyboard */; };
		1A958B1D28017F320088F84F /* LoginViewMode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A958B1C28017F320088F84F /* LoginViewMode.swift */; };
		1A9649E027F7750B0048097C /* Cart.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A9649DF27F7750B0048097C /* Cart.storyboard */; };
		1A9649E227F775760048097C /* OrderNoteVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9649E127F775760048097C /* OrderNoteVC.swift */; };
		1A9649E427F776940048097C /* SignupVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9649E327F776940048097C /* SignupVC.swift */; };
		1A9CDEF727F9A65A001E15E3 /* AddAddressVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9CDEF627F9A65A001E15E3 /* AddAddressVC.swift */; };
		1A9CDEFD27F9BB8C001E15E3 /* TermsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9CDEFC27F9BB8C001E15E3 /* TermsVC.swift */; };
		1A9CDF0027F9CEFD001E15E3 /* CartTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 1A9CDEFE27F9CEFD001E15E3 /* CartTableViewCell.xib */; };
		1A9CDF0127F9CEFD001E15E3 /* CartTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9CDEFF27F9CEFD001E15E3 /* CartTableViewCell.swift */; };
		1A9CDF0327F9EBDE001E15E3 /* ShoppingBagVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9CDF0227F9EBDE001E15E3 /* ShoppingBagVC.swift */; };
		1AB40A1427FAB05B0060A933 /* CheckOutTotelVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1AB40A1327FAB05B0060A933 /* CheckOutTotelVC.swift */; };
		1AB40A1627FAB89E0060A933 /* PaymentVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1AB40A1527FAB89E0060A933 /* PaymentVC.swift */; };
		818279CFEE36AAA135717C3F /* Pods_BabyHouseTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9AA235B690889C5E6152AF3D /* Pods_BabyHouseTests.framework */; };
		C905694E16B5E071105A165B /* Pods_BabyHouse.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 71700FB2B8FB44FB416526C6 /* Pods_BabyHouse.framework */; };
		F3002A0628000FA50059F532 /* AutheticationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3002A0528000F9B0059F532 /* AutheticationRequest.swift */; };
		F30FDFE427EEE61D00674561 /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F30FDFE327EEE61D00674561 /* Home.storyboard */; };
		F30FDFED27EEE63100674561 /* SideinTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FDFE927EEE63100674561 /* SideinTransition.swift */; };
		F30FDFEE27EEE63100674561 /* ProfileTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FDFEA27EEE63100674561 /* ProfileTableViewCell.swift */; };
		F30FDFEF27EEE63100674561 /* ProfileTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F30FDFEB27EEE63100674561 /* ProfileTableViewCell.xib */; };
		F30FDFF027EEE63100674561 /* MenuVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FDFEC27EEE63100674561 /* MenuVC.swift */; };
		F30FDFF227EEE63A00674561 /* HomecontainerVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FDFF127EEE63A00674561 /* HomecontainerVC.swift */; };
		F30FDFF727EEE83D00674561 /* LocalizationSystem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FDFF627EEE83D00674561 /* LocalizationSystem.swift */; };
		F30FE00727EEE8B200674561 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = F30FE00927EEE8B200674561 /* Localizable.strings */; };
		F30FE01227EEEAA100674561 /* HomeVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FE01127EEEAA100674561 /* HomeVC.swift */; };
		F30FE01727EEEAEE00674561 /* HomeVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30FE01627EEEAEE00674561 /* HomeVM.swift */; };
		F33677362805408500896EC1 /* AttributeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F33677342805408500896EC1 /* AttributeTableViewCell.swift */; };
		F33677372805408500896EC1 /* AttributeTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F33677352805408500896EC1 /* AttributeTableViewCell.xib */; };
		F382C9F227F3169B008B9AF6 /* MyAccountVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382C9F027F3169B008B9AF6 /* MyAccountVC.swift */; };
		F382C9F327F3169B008B9AF6 /* Account.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F382C9F127F3169B008B9AF6 /* Account.storyboard */; };
		F382C9FA27F32825008B9AF6 /* CategoryCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382C9F827F32825008B9AF6 /* CategoryCollectionViewCell.swift */; };
		F382C9FB27F32825008B9AF6 /* CategoryCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F382C9F927F32825008B9AF6 /* CategoryCollectionViewCell.xib */; };
		F382CA0427F32D7E008B9AF6 /* AddCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382CA0227F32D7E008B9AF6 /* AddCollectionViewCell.swift */; };
		F382CA0527F32D7E008B9AF6 /* AddCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F382CA0327F32D7E008B9AF6 /* AddCollectionViewCell.xib */; };
		F382CA0B27F331B0008B9AF6 /* newCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382CA0927F331B0008B9AF6 /* newCollectionViewCell.swift */; };
		F382CA0C27F331B0008B9AF6 /* newCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F382CA0A27F331B0008B9AF6 /* newCollectionViewCell.xib */; };
		F382CA1227F33BB6008B9AF6 /* add2CollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382CA1027F33BB6008B9AF6 /* add2CollectionViewCell.swift */; };
		F382CA1327F33BB6008B9AF6 /* add2CollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F382CA1127F33BB6008B9AF6 /* add2CollectionViewCell.xib */; };
		F382CA1927F33D9A008B9AF6 /* FeatureCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382CA1727F33D9A008B9AF6 /* FeatureCollectionViewCell.swift */; };
		F382CA1A27F33D9A008B9AF6 /* FeatureCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F382CA1827F33D9A008B9AF6 /* FeatureCollectionViewCell.xib */; };
		F382CA2827F34E8D008B9AF6 /* BlogCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F382CA2627F34E8D008B9AF6 /* BlogCollectionViewCell.swift */; };
		F382CA2927F34E8D008B9AF6 /* BlogCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F382CA2727F34E8D008B9AF6 /* BlogCollectionViewCell.xib */; };
		F38D264027FAD1580066F9A0 /* Size2CollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F38D263E27FAD1580066F9A0 /* Size2CollectionViewCell.swift */; };
		F38D264127FAD1580066F9A0 /* Size2CollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F38D263F27FAD1580066F9A0 /* Size2CollectionViewCell.xib */; };
		F38D264D27FAD8330066F9A0 /* ColorCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F38D264B27FAD8330066F9A0 /* ColorCollectionViewCell.swift */; };
		F38D264E27FAD8330066F9A0 /* ColorCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F38D264C27FAD8330066F9A0 /* ColorCollectionViewCell.xib */; };
		F395CD8227F81A52000E1852 /* Order.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F395CD8127F81A52000E1852 /* Order.storyboard */; };
		F395CD8A27F82656000E1852 /* LanSelectVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F395CD8727F82656000E1852 /* LanSelectVC.swift */; };
		F395CD8B27F82656000E1852 /* SettingsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F395CD8827F82656000E1852 /* SettingsVC.swift */; };
		F395CD8C27F82656000E1852 /* Settings.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F395CD8927F82656000E1852 /* Settings.storyboard */; };
		F395CD9527F82D4F000E1852 /* NotificationCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F395CD9127F82D4F000E1852 /* NotificationCell.xib */; };
		F395CD9627F82D4F000E1852 /* NotificationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F395CD9227F82D4F000E1852 /* NotificationCell.swift */; };
		F395CD9727F82D4F000E1852 /* NotificationVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F395CD9327F82D4F000E1852 /* NotificationVC.swift */; };
		F395CD9827F82D4F000E1852 /* NotificationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F395CD9427F82D4F000E1852 /* NotificationViewModel.swift */; };
		F3BE4F3B27EB489D00E3328A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4F3A27EB489D00E3328A /* AppDelegate.swift */; };
		F3BE4F3D27EB489D00E3328A /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4F3C27EB489D00E3328A /* SceneDelegate.swift */; };
		F3BE4F4527EB489D00E3328A /* BabyHouse.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4F4327EB489D00E3328A /* BabyHouse.xcdatamodeld */; };
		F3BE4F4727EB48A200E3328A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F3BE4F4627EB48A200E3328A /* Assets.xcassets */; };
		F3BE4F5527EB48A200E3328A /* BabyHouseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4F5427EB48A200E3328A /* BabyHouseTests.swift */; };
		F3BE4F6027EB48A200E3328A /* BabyHouseUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4F5F27EB48A200E3328A /* BabyHouseUITests.swift */; };
		F3BE4FE827EB4CAB00E3328A /* StartCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F3BE4FE227EB4CAB00E3328A /* StartCollectionViewCell.xib */; };
		F3BE4FE927EB4CAB00E3328A /* StartCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FE327EB4CAB00E3328A /* StartCollectionViewCell.swift */; };
		F3BE4FEA27EB4CAB00E3328A /* GetstartVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FE427EB4CAB00E3328A /* GetstartVC.swift */; };
		F3BE4FEB27EB4CAB00E3328A /* SplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FE527EB4CAB00E3328A /* SplashView.swift */; };
		F3BE4FEC27EB4CAB00E3328A /* Splash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F3BE4FE627EB4CAB00E3328A /* Splash.storyboard */; };
		F3BE4FF127EB4CBA00E3328A /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F3BE4FF027EB4CBA00E3328A /* LaunchScreen.storyboard */; };
		F3BE500D27EB4CCC00E3328A /* PickerField.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FF627EB4CCC00E3328A /* PickerField.swift */; };
		F3BE500E27EB4CCC00E3328A /* ShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FF727EB4CCC00E3328A /* ShimmerView.swift */; };
		F3BE500F27EB4CCC00E3328A /* BaseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FF827EB4CCC00E3328A /* BaseViewModel.swift */; };
		F3BE501027EB4CCC00E3328A /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FF927EB4CCC00E3328A /* Extensions.swift */; };
		F3BE501127EB4CCC00E3328A /* Storyboarded.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FFA27EB4CCC00E3328A /* Storyboarded.swift */; };
		F3BE501227EB4CCC00E3328A /* Protocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FFB27EB4CCC00E3328A /* Protocols.swift */; };
		F3BE501327EB4CCC00E3328A /* Badgebutton.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FFC27EB4CCC00E3328A /* Badgebutton.swift */; };
		F3BE501427EB4CCC00E3328A /* ApplicationConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE4FFD27EB4CCC00E3328A /* ApplicationConfiguration.swift */; };
		F3BE501527EB4CCC00E3328A /* Roboto-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F3BE4FFF27EB4CCC00E3328A /* Roboto-Medium.ttf */; };
		F3BE501627EB4CCC00E3328A /* Roboto-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F3BE500027EB4CCC00E3328A /* Roboto-Light.ttf */; };
		F3BE501727EB4CCC00E3328A /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F3BE500127EB4CCC00E3328A /* Roboto-Regular.ttf */; };
		F3BE501827EB4CCC00E3328A /* Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500227EB4CCC00E3328A /* Fonts.swift */; };
		F3BE501927EB4CCC00E3328A /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F3BE500327EB4CCC00E3328A /* Roboto-Bold.ttf */; };
		F3BE501A27EB4CCC00E3328A /* Roboto-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F3BE500427EB4CCC00E3328A /* Roboto-Black.ttf */; };
		F3BE501B27EB4CCC00E3328A /* Colourmanager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500527EB4CCC00E3328A /* Colourmanager.swift */; };
		F3BE501C27EB4CCC00E3328A /* StrokeColorAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500727EB4CCC00E3328A /* StrokeColorAnimation.swift */; };
		F3BE501D27EB4CCC00E3328A /* RotationAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500827EB4CCC00E3328A /* RotationAnimation.swift */; };
		F3BE501E27EB4CCC00E3328A /* ProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500927EB4CCC00E3328A /* ProgressView.swift */; };
		F3BE501F27EB4CCC00E3328A /* StrokeAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500A27EB4CCC00E3328A /* StrokeAnimation.swift */; };
		F3BE502027EB4CCC00E3328A /* ProgressShapeLayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500B27EB4CCC00E3328A /* ProgressShapeLayer.swift */; };
		F3BE502127EB4CCC00E3328A /* MActivityIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE500C27EB4CCC00E3328A /* MActivityIndicator.swift */; };
		F3BE503527EB4D3500E3328A /* NError.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE502F27EB4D3400E3328A /* NError.swift */; };
		F3BE503627EB4D3500E3328A /* Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE503027EB4D3400E3328A /* Helpers.swift */; };
		F3BE503727EB4D3500E3328A /* Network.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE503127EB4D3400E3328A /* Network.swift */; };
		F3BE503827EB4D3500E3328A /* Reachability.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE503227EB4D3400E3328A /* Reachability.swift */; };
		F3BE503927EB4D3500E3328A /* Endpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE503327EB4D3400E3328A /* Endpoint.swift */; };
		F3BE503A27EB4D3500E3328A /* App.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BE503427EB4D3500E3328A /* App.swift */; };
		F3BF2A3527F4162800DAD5B9 /* Profile.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F3BF2A3427F4162800DAD5B9 /* Profile.storyboard */; };
		F3BF2A3A27F4164100DAD5B9 /* EditProfileVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A3927F4164100DAD5B9 /* EditProfileVC.swift */; };
		F3BF2A3F27F416AC00DAD5B9 /* EditProfileViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A3E27F416AC00DAD5B9 /* EditProfileViewModel.swift */; };
		F3BF2A4527F4230100DAD5B9 /* ChangepwdVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A4327F4230100DAD5B9 /* ChangepwdVC.swift */; };
		F3BF2A4627F4230100DAD5B9 /* ChangePasswordVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A4427F4230100DAD5B9 /* ChangePasswordVM.swift */; };
		F3BF2A5127F4292000DAD5B9 /* WishListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A4B27F4292000DAD5B9 /* WishListVC.swift */; };
		F3BF2A5227F4292000DAD5B9 /* WishListCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F3BF2A4D27F4292000DAD5B9 /* WishListCell.xib */; };
		F3BF2A5327F4292000DAD5B9 /* WishListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A4E27F4292000DAD5B9 /* WishListCell.swift */; };
		F3BF2A5427F4292000DAD5B9 /* Wishlist.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F3BF2A4F27F4292000DAD5B9 /* Wishlist.storyboard */; };
		F3BF2A5527F4292000DAD5B9 /* WishlistModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A5027F4292000DAD5B9 /* WishlistModel.swift */; };
		F3BF2A6B27F4476900DAD5B9 /* AddressListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A6227F4476800DAD5B9 /* AddressListCell.swift */; };
		F3BF2A6C27F4476900DAD5B9 /* AddressListCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F3BF2A6327F4476800DAD5B9 /* AddressListCell.xib */; };
		F3BF2A6E27F4476900DAD5B9 /* AddressVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2A6527F4476800DAD5B9 /* AddressVC.swift */; };
		F3BF2A6F27F4476900DAD5B9 /* Address.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F3BF2A6627F4476800DAD5B9 /* Address.storyboard */; };
		F3BF2ACA27F469ED00DAD5B9 /* MyOrdersListCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F3BF2AC327F469ED00DAD5B9 /* MyOrdersListCell.xib */; };
		F3BF2ACB27F469ED00DAD5B9 /* MyOrderVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2AC427F469ED00DAD5B9 /* MyOrderVC.swift */; };
		F3BF2ACC27F469ED00DAD5B9 /* RateUsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2AC527F469ED00DAD5B9 /* RateUsVC.swift */; };
		F3BF2ACD27F469ED00DAD5B9 /* TrackorderVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2AC627F469ED00DAD5B9 /* TrackorderVC.swift */; };
		F3BF2ACF27F469ED00DAD5B9 /* RatingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2AC827F469ED00DAD5B9 /* RatingVC.swift */; };
		F3BF2AD027F469ED00DAD5B9 /* MyOrdersListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3BF2AC927F469ED00DAD5B9 /* MyOrdersListCell.swift */; };
		F3C4827F2800462B00C3047B /* HomeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3C4827E2800462A00C3047B /* HomeModel.swift */; };
		F3C482842800503900C3047B /* Model.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3C482832800503900C3047B /* Model.swift */; };
		F3C6F99127ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99227ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99327ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99427ECB6E60060C854 /* (null) in Resources */ = {isa = PBXBuildFile; };
		F3C6F99527ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99627ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99727ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99827ECB6E60060C854 /* (null) in Resources */ = {isa = PBXBuildFile; };
		F3C6F99927ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3C6F99A27ECB6E60060C854 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F3EA87FB27F9577F00D4EB13 /* ProductdetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3EA87FA27F9577F00D4EB13 /* ProductdetailVC.swift */; };
		F3EA880327F958D200D4EB13 /* ProductImgCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3EA880127F958D200D4EB13 /* ProductImgCollectionViewCell.swift */; };
		F3EA880427F958D200D4EB13 /* ProductImgCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = F3EA880227F958D200D4EB13 /* ProductImgCollectionViewCell.xib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		F3BE4F5127EB48A200E3328A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3BE4F2F27EB489D00E3328A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F3BE4F3627EB489D00E3328A;
			remoteInfo = BabyHouse;
		};
		F3BE4F5C27EB48A200E3328A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3BE4F2F27EB489D00E3328A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F3BE4F3627EB489D00E3328A;
			remoteInfo = BabyHouse;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0314B71A28A2661B001E050E /* TrackTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackTableViewCell.swift; sourceTree = "<group>"; };
		0314B71B28A2661B001E050E /* TrackTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TrackTableViewCell.xib; sourceTree = "<group>"; };
		0314B71E28A2703E001E050E /* PaymentsuccessVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentsuccessVC.swift; sourceTree = "<group>"; };
		0327024A288287040010AEF8 /* Metropolis-Medium.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Metropolis-Medium.otf"; sourceTree = "<group>"; };
		0327024C288287130010AEF8 /* Metropolis-Regular.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Metropolis-Regular.otf"; sourceTree = "<group>"; };
		0327024E2882871F0010AEF8 /* Metropolis-Bold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Metropolis-Bold.otf"; sourceTree = "<group>"; };
		03270250288287290010AEF8 /* Metropolis-Black.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Metropolis-Black.otf"; sourceTree = "<group>"; };
		03270252288287340010AEF8 /* Metropolis-Light.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Metropolis-Light.otf"; sourceTree = "<group>"; };
		0332651D2886990900927B09 /* TagCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagCollectionViewCell.swift; sourceTree = "<group>"; };
		0332651E2886990900927B09 /* TagCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TagCollectionViewCell.xib; sourceTree = "<group>"; };
		033265212886C11A00927B09 /* MapVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MapVC.swift; sourceTree = "<group>"; };
		033301CC288144430037A7BD /* login.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = login.json; sourceTree = "<group>"; };
		033333F02850708500D95000 /* ChoosecurrencyVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChoosecurrencyVC.swift; sourceTree = "<group>"; };
		0358F0402887CE9A00A8CF80 /* AreaVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AreaVC.swift; sourceTree = "<group>"; };
		0358F0422887CEDC00A8CF80 /* AreaTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AreaTableViewCell.swift; sourceTree = "<group>"; };
		0358F0432887CEDC00A8CF80 /* AreaTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AreaTableViewCell.xib; sourceTree = "<group>"; };
		035FD1EF287AAF3C00910980 /* BlogVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlogVC.swift; sourceTree = "<group>"; };
		035FD1F1287AB0A600910980 /* BlogTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlogTableViewCell.swift; sourceTree = "<group>"; };
		035FD1F2287AB0A600910980 /* BlogTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BlogTableViewCell.xib; sourceTree = "<group>"; };
		035FD1F5287AB55900910980 /* ProductListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductListVC.swift; sourceTree = "<group>"; };
		036A2BBB28A21F5F0085AE87 /* SearchVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SearchVC.swift; sourceTree = "<group>"; };
		036A2BC028A21F750085AE87 /* SearchModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchModel.swift; sourceTree = "<group>"; };
		036A2BC628A2211F0085AE87 /* ProductsearchTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductsearchTableViewCell.swift; sourceTree = "<group>"; };
		036A2BC728A2211F0085AE87 /* ProductsearchTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProductsearchTableViewCell.xib; sourceTree = "<group>"; };
		036A2BCA28A222020085AE87 /* Search.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Search.storyboard; sourceTree = "<group>"; };
		036D7B4E285773A50055F427 /* ChoosecountryVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChoosecountryVC.swift; sourceTree = "<group>"; };
		03950D3B284F2E0700D7C19E /* CattitleCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CattitleCollectionViewCell.swift; sourceTree = "<group>"; };
		03950D3C284F2E0700D7C19E /* CattitleCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CattitleCollectionViewCell.xib; sourceTree = "<group>"; };
		03A1E4F628479EEE007B955A /* CategoryprodutsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryprodutsVC.swift; sourceTree = "<group>"; };
		161308BDF72684194B9B5679 /* Pods-BabyHouse-BabyHouseUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BabyHouse-BabyHouseUITests.release.xcconfig"; path = "Target Support Files/Pods-BabyHouse-BabyHouseUITests/Pods-BabyHouse-BabyHouseUITests.release.xcconfig"; sourceTree = "<group>"; };
		182521732AEC066800402770 /* PromoListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PromoListView.swift; sourceTree = "<group>"; };
		182521792AEC131600402770 /* OtpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OtpView.swift; sourceTree = "<group>"; };
		1825217B2AEC134500402770 /* OtpViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OtpViewModel.swift; sourceTree = "<group>"; };
		1849E1382AB8314700751BE3 /* BabyHouse.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = BabyHouse.entitlements; sourceTree = "<group>"; };
		1865DB7A2C7A3C8900C7C9D7 /* CarouselView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CarouselView.swift; sourceTree = "<group>"; };
		1871A1912AA0DC2B00978E37 /* Notification.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Notification.swift; sourceTree = "<group>"; };
		1871A1942AA0E95D00978E37 /* ProductDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductDetailView.swift; sourceTree = "<group>"; };
		1871A1962AA0F82200978E37 /* ViewWrapper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewWrapper.swift; sourceTree = "<group>"; };
		1882ECCC2AAF6FF700186C87 /* PaymentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentView.swift; sourceTree = "<group>"; };
		1882ECCE2AAF969A00186C87 /* PaymentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentModel.swift; sourceTree = "<group>"; };
		1882ECD22AAF9E9300186C87 /* PaymentViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentViewModel.swift; sourceTree = "<group>"; };
		18A3137B2E5081DF0036A202 /* APITestingGuide.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APITestingGuide.swift; sourceTree = "<group>"; };
		18A3137C2E5081DF0036A202 /* AuthenticationLogger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationLogger.swift; sourceTree = "<group>"; };
		18AD6D912ACC07AB00A2903B /* KeychainItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainItem.swift; sourceTree = "<group>"; };
		18B4C4F42AB0B1210062735F /* LoyaltyCashView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoyaltyCashView.swift; sourceTree = "<group>"; };
		18B4C4F62AB0B1A50062735F /* InviteFriendsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteFriendsView.swift; sourceTree = "<group>"; };
		18B4C4F72AB0B1A80062735F /* WalletView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletView.swift; sourceTree = "<group>"; };
		18B4C4FB2AB0B91D0062735F /* BadgeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BadgeView.swift; sourceTree = "<group>"; };
		18B4C4FE2AB0C5C00062735F /* NavigationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationView.swift; sourceTree = "<group>"; };
		18B6968A2AB1A67900879BA6 /* Font.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Font.swift; sourceTree = "<group>"; };
		18B6968C2AB1A71F00879BA6 /* Metropolis-SemiBold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Metropolis-SemiBold.otf"; sourceTree = "<group>"; };
		18B6968F2AB1C45900879BA6 /* OrderListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderListView.swift; sourceTree = "<group>"; };
		18B696912AB1C48E00879BA6 /* OrderListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderListViewModel.swift; sourceTree = "<group>"; };
		18B696932AB1C4B300879BA6 /* OrderListModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderListModel.swift; sourceTree = "<group>"; };
		18B696952AB1C53900879BA6 /* MyOrderListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyOrderListVC.swift; sourceTree = "<group>"; };
		18B696972AB1D33900879BA6 /* WalletViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletViewModel.swift; sourceTree = "<group>"; };
		18B696992AB1D34200879BA6 /* WalletModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletModel.swift; sourceTree = "<group>"; };
		18B6969B2AB1D35C00879BA6 /* LoyaltyCashViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoyaltyCashViewModel.swift; sourceTree = "<group>"; };
		18B6969D2AB1D36500879BA6 /* LoyaltyCashModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoyaltyCashModel.swift; sourceTree = "<group>"; };
		18BA514B2AAB378F002FEC90 /* custom_loader.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = custom_loader.json; sourceTree = "<group>"; };
		18BA514F2AAB3FA2002FEC90 /* splash_screen.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = splash_screen.json; sourceTree = "<group>"; };
		18BA51512AAB5ECB002FEC90 /* SendEnquiryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SendEnquiryView.swift; sourceTree = "<group>"; };
		18BA51532AAB643D002FEC90 /* HomeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeViewModel.swift; sourceTree = "<group>"; };
		18BFCAC92AC2E9DB00B00222 /* WebContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebContentView.swift; sourceTree = "<group>"; };
		18CBE87F2AB5C79F00B5BF57 /* ShareEarnModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareEarnModel.swift; sourceTree = "<group>"; };
		18CBE8812AB5C82800B5BF57 /* InviteFriendsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteFriendsViewModel.swift; sourceTree = "<group>"; };
		18CC10D02AA31D9C00C2C1E3 /* ProductDetailViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductDetailViewModel.swift; sourceTree = "<group>"; };
		18CC10D42AA321B700C2C1E3 /* SuperView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperView.swift; sourceTree = "<group>"; };
		18CC10D72AA3221100C2C1E3 /* SuperModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperModel.swift; sourceTree = "<group>"; };
		18CC10DC2AA322AF00C2C1E3 /* AlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertView.swift; sourceTree = "<group>"; };
		18CC10DF2AA322F200C2C1E3 /* Utilities.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Utilities.swift; sourceTree = "<group>"; };
		18CC10E32AA3238C00C2C1E3 /* ActivityLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActivityLoader.swift; sourceTree = "<group>"; };
		18CC10E62AA323D800C2C1E3 /* LottieView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LottieView.swift; sourceTree = "<group>"; };
		18CC10EC2AA3331F00C2C1E3 /* CustomDotsIndexView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomDotsIndexView.swift; sourceTree = "<group>"; };
		18CC10EF2AA3389C00C2C1E3 /* ViewportHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewportHelper.swift; sourceTree = "<group>"; };
		18CC10F22AA33B7C00C2C1E3 /* BabyHouseImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BabyHouseImageView.swift; sourceTree = "<group>"; };
		18CC10F72AA33CD400C2C1E3 /* View.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = View.swift; sourceTree = "<group>"; };
		18CC10FB2AA35AD500C2C1E3 /* AppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppState.swift; sourceTree = "<group>"; };
		18CC10FD2AA3672C00C2C1E3 /* Color.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Color.swift; sourceTree = "<group>"; };
		18D09E542ACAC02900D3511B /* CollectionViewFlowLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionViewFlowLayout.swift; sourceTree = "<group>"; };
		18D2F5172AC43F0300F6BECD /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		18D2F5192AC43F4000F6BECD /* WebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebView.swift; sourceTree = "<group>"; };
		18D2F51D2AC43FB700F6BECD /* WebContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebContainer.swift; sourceTree = "<group>"; };
		18DC2ED72AEFE52E006E3D5D /* ResetPasswordVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResetPasswordVC.swift; sourceTree = "<group>"; };
		18E121CA2AECF9ED007D3C13 /* ForgetPasswordVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgetPasswordVC.swift; sourceTree = "<group>"; };
		18E121CC2AED1EEB007D3C13 /* VerifyOtpVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerifyOtpVC.swift; sourceTree = "<group>"; };
		18E980F62BB186BA00ACF8FD /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		18EFC2E72AB9BE0B0029010D /* GovernorateVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GovernorateVC.swift; sourceTree = "<group>"; };
		1A852A0028029B890074191B /* SignupVM.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignupVM.swift; sourceTree = "<group>"; };
		1A8A17D1285F8A4B0089CE04 /* MyOrderVM.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyOrderVM.swift; sourceTree = "<group>"; };
		1A8A17D3285F8B260089CE04 /* OrderRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderRequest.swift; sourceTree = "<group>"; };
		1A8A17D5285F9B1D0089CE04 /* AddressVM.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressVM.swift; sourceTree = "<group>"; };
		1A8A17D7285F9B620089CE04 /* AddressRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressRequest.swift; sourceTree = "<group>"; };
		1A9177AB27F3754D003EA672 /* TitleTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TitleTableViewCell.swift; sourceTree = "<group>"; };
		1A9177AC27F3754D003EA672 /* TitleTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TitleTableViewCell.xib; sourceTree = "<group>"; };
		1A9177AD27F3754D003EA672 /* CategoryTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CategoryTableViewCell.xib; sourceTree = "<group>"; };
		1A9177AE27F3754D003EA672 /* Category.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Category.storyboard; sourceTree = "<group>"; };
		1A9177AF27F3754D003EA672 /* CategoryVM.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CategoryVM.swift; sourceTree = "<group>"; };
		1A9177B027F3754D003EA672 /* CategoryVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CategoryVC.swift; sourceTree = "<group>"; };
		1A9177B127F3754D003EA672 /* CategoryTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CategoryTableViewCell.swift; sourceTree = "<group>"; };
		1A9177BA27F37F55003EA672 /* Login.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Login.storyboard; sourceTree = "<group>"; };
		1A9177BC27F38917003EA672 /* LoginVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginVC.swift; sourceTree = "<group>"; };
		1A9177BF27F38B16003EA672 /* Payment.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Payment.storyboard; sourceTree = "<group>"; };
		1A958B1C28017F320088F84F /* LoginViewMode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewMode.swift; sourceTree = "<group>"; };
		1A9649DF27F7750B0048097C /* Cart.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Cart.storyboard; sourceTree = "<group>"; };
		1A9649E127F775760048097C /* OrderNoteVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderNoteVC.swift; sourceTree = "<group>"; };
		1A9649E327F776940048097C /* SignupVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignupVC.swift; sourceTree = "<group>"; };
		1A9CDEF627F9A65A001E15E3 /* AddAddressVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAddressVC.swift; sourceTree = "<group>"; };
		1A9CDEFC27F9BB8C001E15E3 /* TermsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TermsVC.swift; sourceTree = "<group>"; };
		1A9CDEFE27F9CEFD001E15E3 /* CartTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CartTableViewCell.xib; sourceTree = "<group>"; };
		1A9CDEFF27F9CEFD001E15E3 /* CartTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CartTableViewCell.swift; sourceTree = "<group>"; };
		1A9CDF0227F9EBDE001E15E3 /* ShoppingBagVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShoppingBagVC.swift; sourceTree = "<group>"; };
		1AB40A1327FAB05B0060A933 /* CheckOutTotelVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckOutTotelVC.swift; sourceTree = "<group>"; };
		1AB40A1527FAB89E0060A933 /* PaymentVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentVC.swift; sourceTree = "<group>"; };
		4E69950C7D98FA336A024384 /* Pods-BabyHouse.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BabyHouse.release.xcconfig"; path = "Target Support Files/Pods-BabyHouse/Pods-BabyHouse.release.xcconfig"; sourceTree = "<group>"; };
		71700FB2B8FB44FB416526C6 /* Pods_BabyHouse.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_BabyHouse.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		94CCB6FC9E964BD509D68E01 /* Pods-BabyHouse.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BabyHouse.debug.xcconfig"; path = "Target Support Files/Pods-BabyHouse/Pods-BabyHouse.debug.xcconfig"; sourceTree = "<group>"; };
		95AA07DFA3388DC425A0AB27 /* Pods_BabyHouse_BabyHouseUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_BabyHouse_BabyHouseUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95FD843B48D334997175438C /* Pods-BabyHouseTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BabyHouseTests.release.xcconfig"; path = "Target Support Files/Pods-BabyHouseTests/Pods-BabyHouseTests.release.xcconfig"; sourceTree = "<group>"; };
		9AA235B690889C5E6152AF3D /* Pods_BabyHouseTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_BabyHouseTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C2583E2B309400F54A29014A /* Pods-BabyHouseTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BabyHouseTests.debug.xcconfig"; path = "Target Support Files/Pods-BabyHouseTests/Pods-BabyHouseTests.debug.xcconfig"; sourceTree = "<group>"; };
		C575EBD8C8249320B2E9C348 /* Pods-BabyHouse-BabyHouseUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BabyHouse-BabyHouseUITests.debug.xcconfig"; path = "Target Support Files/Pods-BabyHouse-BabyHouseUITests/Pods-BabyHouse-BabyHouseUITests.debug.xcconfig"; sourceTree = "<group>"; };
		F3002A0528000F9B0059F532 /* AutheticationRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AutheticationRequest.swift; sourceTree = "<group>"; };
		F30FDFE327EEE61D00674561 /* Home.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Home.storyboard; sourceTree = "<group>"; };
		F30FDFE927EEE63100674561 /* SideinTransition.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SideinTransition.swift; sourceTree = "<group>"; };
		F30FDFEA27EEE63100674561 /* ProfileTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProfileTableViewCell.swift; sourceTree = "<group>"; };
		F30FDFEB27EEE63100674561 /* ProfileTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ProfileTableViewCell.xib; sourceTree = "<group>"; };
		F30FDFEC27EEE63100674561 /* MenuVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MenuVC.swift; sourceTree = "<group>"; };
		F30FDFF127EEE63A00674561 /* HomecontainerVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomecontainerVC.swift; sourceTree = "<group>"; };
		F30FDFF627EEE83D00674561 /* LocalizationSystem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LocalizationSystem.swift; sourceTree = "<group>"; };
		F30FE00827EEE8B200674561 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		F30FE00D27EEE8B600674561 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		F30FE01127EEEAA100674561 /* HomeVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeVC.swift; sourceTree = "<group>"; };
		F30FE01627EEEAEE00674561 /* HomeVM.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeVM.swift; sourceTree = "<group>"; };
		F33677342805408500896EC1 /* AttributeTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttributeTableViewCell.swift; sourceTree = "<group>"; };
		F33677352805408500896EC1 /* AttributeTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AttributeTableViewCell.xib; sourceTree = "<group>"; };
		F382C9F027F3169B008B9AF6 /* MyAccountVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyAccountVC.swift; sourceTree = "<group>"; };
		F382C9F127F3169B008B9AF6 /* Account.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Account.storyboard; sourceTree = "<group>"; };
		F382C9F827F32825008B9AF6 /* CategoryCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryCollectionViewCell.swift; sourceTree = "<group>"; };
		F382C9F927F32825008B9AF6 /* CategoryCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CategoryCollectionViewCell.xib; sourceTree = "<group>"; };
		F382CA0227F32D7E008B9AF6 /* AddCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddCollectionViewCell.swift; sourceTree = "<group>"; };
		F382CA0327F32D7E008B9AF6 /* AddCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AddCollectionViewCell.xib; sourceTree = "<group>"; };
		F382CA0927F331B0008B9AF6 /* newCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = newCollectionViewCell.swift; sourceTree = "<group>"; };
		F382CA0A27F331B0008B9AF6 /* newCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = newCollectionViewCell.xib; sourceTree = "<group>"; };
		F382CA1027F33BB6008B9AF6 /* add2CollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = add2CollectionViewCell.swift; sourceTree = "<group>"; };
		F382CA1127F33BB6008B9AF6 /* add2CollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = add2CollectionViewCell.xib; sourceTree = "<group>"; };
		F382CA1727F33D9A008B9AF6 /* FeatureCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeatureCollectionViewCell.swift; sourceTree = "<group>"; };
		F382CA1827F33D9A008B9AF6 /* FeatureCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FeatureCollectionViewCell.xib; sourceTree = "<group>"; };
		F382CA2627F34E8D008B9AF6 /* BlogCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlogCollectionViewCell.swift; sourceTree = "<group>"; };
		F382CA2727F34E8D008B9AF6 /* BlogCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BlogCollectionViewCell.xib; sourceTree = "<group>"; };
		F38D263E27FAD1580066F9A0 /* Size2CollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Size2CollectionViewCell.swift; sourceTree = "<group>"; };
		F38D263F27FAD1580066F9A0 /* Size2CollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = Size2CollectionViewCell.xib; sourceTree = "<group>"; };
		F38D264B27FAD8330066F9A0 /* ColorCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorCollectionViewCell.swift; sourceTree = "<group>"; };
		F38D264C27FAD8330066F9A0 /* ColorCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ColorCollectionViewCell.xib; sourceTree = "<group>"; };
		F395CD8127F81A52000E1852 /* Order.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Order.storyboard; sourceTree = "<group>"; };
		F395CD8727F82656000E1852 /* LanSelectVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LanSelectVC.swift; sourceTree = "<group>"; };
		F395CD8827F82656000E1852 /* SettingsVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingsVC.swift; sourceTree = "<group>"; };
		F395CD8927F82656000E1852 /* Settings.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Settings.storyboard; sourceTree = "<group>"; };
		F395CD9127F82D4F000E1852 /* NotificationCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = NotificationCell.xib; sourceTree = "<group>"; };
		F395CD9227F82D4F000E1852 /* NotificationCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationCell.swift; sourceTree = "<group>"; };
		F395CD9327F82D4F000E1852 /* NotificationVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationVC.swift; sourceTree = "<group>"; };
		F395CD9427F82D4F000E1852 /* NotificationViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationViewModel.swift; sourceTree = "<group>"; };
		F3BE4F3727EB489D00E3328A /* BabyHouse.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BabyHouse.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3BE4F3A27EB489D00E3328A /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		F3BE4F3C27EB489D00E3328A /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		F3BE4F4427EB489D00E3328A /* BabyHouse.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = BabyHouse.xcdatamodel; sourceTree = "<group>"; };
		F3BE4F4627EB48A200E3328A /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F3BE4F4B27EB48A200E3328A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F3BE4F5027EB48A200E3328A /* BabyHouseTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BabyHouseTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F3BE4F5427EB48A200E3328A /* BabyHouseTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BabyHouseTests.swift; sourceTree = "<group>"; };
		F3BE4F5627EB48A200E3328A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F3BE4F5B27EB48A200E3328A /* BabyHouseUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BabyHouseUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F3BE4F5F27EB48A200E3328A /* BabyHouseUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BabyHouseUITests.swift; sourceTree = "<group>"; };
		F3BE4F6127EB48A200E3328A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F3BE4FE227EB4CAB00E3328A /* StartCollectionViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = StartCollectionViewCell.xib; sourceTree = "<group>"; };
		F3BE4FE327EB4CAB00E3328A /* StartCollectionViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StartCollectionViewCell.swift; sourceTree = "<group>"; };
		F3BE4FE427EB4CAB00E3328A /* GetstartVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetstartVC.swift; sourceTree = "<group>"; };
		F3BE4FE527EB4CAB00E3328A /* SplashView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SplashView.swift; sourceTree = "<group>"; };
		F3BE4FE627EB4CAB00E3328A /* Splash.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Splash.storyboard; sourceTree = "<group>"; };
		F3BE4FF027EB4CBA00E3328A /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		F3BE4FF627EB4CCC00E3328A /* PickerField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PickerField.swift; sourceTree = "<group>"; };
		F3BE4FF727EB4CCC00E3328A /* ShimmerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShimmerView.swift; sourceTree = "<group>"; };
		F3BE4FF827EB4CCC00E3328A /* BaseViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseViewModel.swift; sourceTree = "<group>"; };
		F3BE4FF927EB4CCC00E3328A /* Extensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		F3BE4FFA27EB4CCC00E3328A /* Storyboarded.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Storyboarded.swift; sourceTree = "<group>"; };
		F3BE4FFB27EB4CCC00E3328A /* Protocols.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Protocols.swift; sourceTree = "<group>"; };
		F3BE4FFC27EB4CCC00E3328A /* Badgebutton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Badgebutton.swift; sourceTree = "<group>"; };
		F3BE4FFD27EB4CCC00E3328A /* ApplicationConfiguration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ApplicationConfiguration.swift; sourceTree = "<group>"; };
		F3BE4FFF27EB4CCC00E3328A /* Roboto-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Medium.ttf"; sourceTree = "<group>"; };
		F3BE500027EB4CCC00E3328A /* Roboto-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Light.ttf"; sourceTree = "<group>"; };
		F3BE500127EB4CCC00E3328A /* Roboto-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Regular.ttf"; sourceTree = "<group>"; };
		F3BE500227EB4CCC00E3328A /* Fonts.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Fonts.swift; sourceTree = "<group>"; };
		F3BE500327EB4CCC00E3328A /* Roboto-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Bold.ttf"; sourceTree = "<group>"; };
		F3BE500427EB4CCC00E3328A /* Roboto-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Black.ttf"; sourceTree = "<group>"; };
		F3BE500527EB4CCC00E3328A /* Colourmanager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Colourmanager.swift; sourceTree = "<group>"; };
		F3BE500727EB4CCC00E3328A /* StrokeColorAnimation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StrokeColorAnimation.swift; sourceTree = "<group>"; };
		F3BE500827EB4CCC00E3328A /* RotationAnimation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RotationAnimation.swift; sourceTree = "<group>"; };
		F3BE500927EB4CCC00E3328A /* ProgressView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProgressView.swift; sourceTree = "<group>"; };
		F3BE500A27EB4CCC00E3328A /* StrokeAnimation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StrokeAnimation.swift; sourceTree = "<group>"; };
		F3BE500B27EB4CCC00E3328A /* ProgressShapeLayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProgressShapeLayer.swift; sourceTree = "<group>"; };
		F3BE500C27EB4CCC00E3328A /* MActivityIndicator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MActivityIndicator.swift; sourceTree = "<group>"; };
		F3BE502F27EB4D3400E3328A /* NError.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NError.swift; sourceTree = "<group>"; };
		F3BE503027EB4D3400E3328A /* Helpers.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Helpers.swift; sourceTree = "<group>"; };
		F3BE503127EB4D3400E3328A /* Network.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Network.swift; sourceTree = "<group>"; };
		F3BE503227EB4D3400E3328A /* Reachability.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Reachability.swift; sourceTree = "<group>"; };
		F3BE503327EB4D3400E3328A /* Endpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Endpoint.swift; sourceTree = "<group>"; };
		F3BE503427EB4D3500E3328A /* App.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = App.swift; sourceTree = "<group>"; };
		F3BF2A3427F4162800DAD5B9 /* Profile.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Profile.storyboard; sourceTree = "<group>"; };
		F3BF2A3927F4164100DAD5B9 /* EditProfileVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EditProfileVC.swift; sourceTree = "<group>"; };
		F3BF2A3E27F416AC00DAD5B9 /* EditProfileViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EditProfileViewModel.swift; sourceTree = "<group>"; };
		F3BF2A4327F4230100DAD5B9 /* ChangepwdVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangepwdVC.swift; sourceTree = "<group>"; };
		F3BF2A4427F4230100DAD5B9 /* ChangePasswordVM.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePasswordVM.swift; sourceTree = "<group>"; };
		F3BF2A4B27F4292000DAD5B9 /* WishListVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WishListVC.swift; sourceTree = "<group>"; };
		F3BF2A4D27F4292000DAD5B9 /* WishListCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = WishListCell.xib; sourceTree = "<group>"; };
		F3BF2A4E27F4292000DAD5B9 /* WishListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WishListCell.swift; sourceTree = "<group>"; };
		F3BF2A4F27F4292000DAD5B9 /* Wishlist.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Wishlist.storyboard; sourceTree = "<group>"; };
		F3BF2A5027F4292000DAD5B9 /* WishlistModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WishlistModel.swift; sourceTree = "<group>"; };
		F3BF2A6227F4476800DAD5B9 /* AddressListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddressListCell.swift; sourceTree = "<group>"; };
		F3BF2A6327F4476800DAD5B9 /* AddressListCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AddressListCell.xib; sourceTree = "<group>"; };
		F3BF2A6527F4476800DAD5B9 /* AddressVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddressVC.swift; sourceTree = "<group>"; };
		F3BF2A6627F4476800DAD5B9 /* Address.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Address.storyboard; sourceTree = "<group>"; };
		F3BF2AC327F469ED00DAD5B9 /* MyOrdersListCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MyOrdersListCell.xib; sourceTree = "<group>"; };
		F3BF2AC427F469ED00DAD5B9 /* MyOrderVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyOrderVC.swift; sourceTree = "<group>"; };
		F3BF2AC527F469ED00DAD5B9 /* RateUsVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RateUsVC.swift; sourceTree = "<group>"; };
		F3BF2AC627F469ED00DAD5B9 /* TrackorderVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TrackorderVC.swift; sourceTree = "<group>"; };
		F3BF2AC827F469ED00DAD5B9 /* RatingVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RatingVC.swift; sourceTree = "<group>"; };
		F3BF2AC927F469ED00DAD5B9 /* MyOrdersListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyOrdersListCell.swift; sourceTree = "<group>"; };
		F3C4827E2800462A00C3047B /* HomeModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeModel.swift; sourceTree = "<group>"; };
		F3C482832800503900C3047B /* Model.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Model.swift; sourceTree = "<group>"; };
		F3EA87FA27F9577F00D4EB13 /* ProductdetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductdetailVC.swift; sourceTree = "<group>"; };
		F3EA880127F958D200D4EB13 /* ProductImgCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductImgCollectionViewCell.swift; sourceTree = "<group>"; };
		F3EA880227F958D200D4EB13 /* ProductImgCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProductImgCollectionViewCell.xib; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		F3BE4F3427EB489D00E3328A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				18D2F5152AC411A300F6BECD /* SwiftyJSON in Frameworks */,
				18BA51492AAAFA88002FEC90 /* WrappingHStack in Frameworks */,
				1865DB812C7A4CD600C7C9D7 /* SwiftUIInfiniteCarousel in Frameworks */,
				182521722AEBC5C000402770 /* FirebaseMessaging in Frameworks */,
				18CC10F62AA33BA800C2C1E3 /* SDWebImageSwiftUI in Frameworks */,
				18AD6D9A2ACC13F100A2903B /* GoogleSignInSwift in Frameworks */,
				1865DB792C7A351B00C7C9D7 /* InfiniteCarousel in Frameworks */,
				18AD6D9D2ACC179500A2903B /* FirebaseAuth in Frameworks */,
				18D8E65F2AA5D43900189C95 /* RichText in Frameworks */,
				1849E13D2AB8491300751BE3 /* Siren in Frameworks */,
				C905694E16B5E071105A165B /* Pods_BabyHouse.framework in Frameworks */,
				18AD6D982ACC13F100A2903B /* GoogleSignIn in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BE4F4D27EB48A200E3328A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				818279CFEE36AAA135717C3F /* Pods_BabyHouseTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BE4F5827EB48A200E3328A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				07E418F3B6AB1EAC37DAE6E7 /* Pods_BabyHouse_BabyHouseUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0332651C288698EB00927B09 /* Add_address */ = {
			isa = PBXGroup;
			children = (
				033265212886C11A00927B09 /* MapVC.swift */,
				1A9CDEF627F9A65A001E15E3 /* AddAddressVC.swift */,
				0332651D2886990900927B09 /* TagCollectionViewCell.swift */,
				0332651E2886990900927B09 /* TagCollectionViewCell.xib */,
				0358F0402887CE9A00A8CF80 /* AreaVC.swift */,
				18EFC2E72AB9BE0B0029010D /* GovernorateVC.swift */,
				0358F0422887CEDC00A8CF80 /* AreaTableViewCell.swift */,
				0358F0432887CEDC00A8CF80 /* AreaTableViewCell.xib */,
			);
			path = Add_address;
			sourceTree = "<group>";
		};
		036A2BBA28A21F520085AE87 /* Search */ = {
			isa = PBXGroup;
			children = (
				036A2BC528A220130085AE87 /* Cell */,
				036A2BC028A21F750085AE87 /* SearchModel.swift */,
				036A2BBB28A21F5F0085AE87 /* SearchVC.swift */,
				036A2BCA28A222020085AE87 /* Search.storyboard */,
			);
			path = Search;
			sourceTree = "<group>";
		};
		036A2BC528A220130085AE87 /* Cell */ = {
			isa = PBXGroup;
			children = (
				036A2BC628A2211F0085AE87 /* ProductsearchTableViewCell.swift */,
				036A2BC728A2211F0085AE87 /* ProductsearchTableViewCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		06546E23A31169FEE166F8F4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				71700FB2B8FB44FB416526C6 /* Pods_BabyHouse.framework */,
				95AA07DFA3388DC425A0AB27 /* Pods_BabyHouse_BabyHouseUITests.framework */,
				9AA235B690889C5E6152AF3D /* Pods_BabyHouseTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		182521752AEC12DC00402770 /* Otp */ = {
			isa = PBXGroup;
			children = (
				182521762AEC12F700402770 /* Model */,
				182521772AEC12FD00402770 /* View */,
				182521782AEC130100402770 /* ViewModel */,
			);
			path = Otp;
			sourceTree = "<group>";
		};
		182521762AEC12F700402770 /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			path = Model;
			sourceTree = "<group>";
		};
		182521772AEC12FD00402770 /* View */ = {
			isa = PBXGroup;
			children = (
				182521792AEC131600402770 /* OtpView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		182521782AEC130100402770 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				1825217B2AEC134500402770 /* OtpViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		1871A18F2AA0DC0E00978E37 /* Global */ = {
			isa = PBXGroup;
			children = (
				18CC10D92AA3226F00C2C1E3 /* Components */,
				1871A1902AA0DC1900978E37 /* Extensions */,
				18CC10DE2AA322E900C2C1E3 /* Utilities */,
				18CC10E12AA3237C00C2C1E3 /* Helper */,
				18EFC2E62AB973AA0029010D /* Frameworks */,
			);
			path = Global;
			sourceTree = "<group>";
		};
		1871A1902AA0DC1900978E37 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				1871A1912AA0DC2B00978E37 /* Notification.swift */,
				18CC10F72AA33CD400C2C1E3 /* View.swift */,
				18CC10FD2AA3672C00C2C1E3 /* Color.swift */,
				18B6968A2AB1A67900879BA6 /* Font.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		1871A1932AA0E94200978E37 /* View */ = {
			isa = PBXGroup;
			children = (
				1871A1942AA0E95D00978E37 /* ProductDetailView.swift */,
				1871A1962AA0F82200978E37 /* ViewWrapper.swift */,
				F33677352805408500896EC1 /* AttributeTableViewCell.xib */,
			);
			path = View;
			sourceTree = "<group>";
		};
		1882ECC92AAF6FBE00186C87 /* Payment */ = {
			isa = PBXGroup;
			children = (
				1882ECD02AAF96A900186C87 /* Model */,
				1882ECD12AAF96B100186C87 /* View */,
				1882ECD42AAF9F3800186C87 /* ViewModel */,
			);
			path = Payment;
			sourceTree = "<group>";
		};
		1882ECD02AAF96A900186C87 /* Model */ = {
			isa = PBXGroup;
			children = (
				1882ECCE2AAF969A00186C87 /* PaymentModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		1882ECD12AAF96B100186C87 /* View */ = {
			isa = PBXGroup;
			children = (
				1882ECCC2AAF6FF700186C87 /* PaymentView.swift */,
				182521732AEC066800402770 /* PromoListView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		1882ECD42AAF9F3800186C87 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				1882ECD22AAF9E9300186C87 /* PaymentViewModel.swift */,
				18D2F5162AC43EF300F6BECD /* WebView */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18B4C4F12AB0B0DC0062735F /* LoyaltyCash */ = {
			isa = PBXGroup;
			children = (
				18B6969D2AB1D36500879BA6 /* LoyaltyCashModel.swift */,
				18B4C4F42AB0B1210062735F /* LoyaltyCashView.swift */,
				18B6969B2AB1D35C00879BA6 /* LoyaltyCashViewModel.swift */,
			);
			path = LoyaltyCash;
			sourceTree = "<group>";
		};
		18B4C4F22AB0B0E40062735F /* InviteFriends */ = {
			isa = PBXGroup;
			children = (
				18CBE87F2AB5C79F00B5BF57 /* ShareEarnModel.swift */,
				18B4C4F62AB0B1A50062735F /* InviteFriendsView.swift */,
				18CBE8812AB5C82800B5BF57 /* InviteFriendsViewModel.swift */,
			);
			path = InviteFriends;
			sourceTree = "<group>";
		};
		18B4C4F32AB0B0EF0062735F /* Wallet */ = {
			isa = PBXGroup;
			children = (
				18B696992AB1D34200879BA6 /* WalletModel.swift */,
				18B4C4F72AB0B1A80062735F /* WalletView.swift */,
				18B696972AB1D33900879BA6 /* WalletViewModel.swift */,
			);
			path = Wallet;
			sourceTree = "<group>";
		};
		18B4C4FA2AB0B90A0062735F /* Badge */ = {
			isa = PBXGroup;
			children = (
				18B4C4FD2AB0B9280062735F /* View */,
			);
			path = Badge;
			sourceTree = "<group>";
		};
		18B4C4FD2AB0B9280062735F /* View */ = {
			isa = PBXGroup;
			children = (
				18B4C4FB2AB0B91D0062735F /* BadgeView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18B6968E2AB1C44400879BA6 /* OrderItems */ = {
			isa = PBXGroup;
			children = (
				18B696932AB1C4B300879BA6 /* OrderListModel.swift */,
				18B6968F2AB1C45900879BA6 /* OrderListView.swift */,
				18B696912AB1C48E00879BA6 /* OrderListViewModel.swift */,
			);
			path = OrderItems;
			sourceTree = "<group>";
		};
		18CC10CF2AA31D7E00C2C1E3 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				F3EA87FA27F9577F00D4EB13 /* ProductdetailVC.swift */,
				18CC10D02AA31D9C00C2C1E3 /* ProductDetailViewModel.swift */,
				F33677342805408500896EC1 /* AttributeTableViewCell.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18CC10D22AA3213F00C2C1E3 /* Module */ = {
			isa = PBXGroup;
			children = (
				18CC10D32AA3216F00C2C1E3 /* SuperBase */,
				F3BE4FDD27EB4CAB00E3328A /* Splash */,
				F30FDFE227EEE55500674561 /* Home */,
				1A9177AA27F3752C003EA672 /* Categories */,
				F3BF2A4A27F4292000DAD5B9 /* WishList */,
				F3BF2A3327F4161500DAD5B9 /* Profile */,
				F382C9EF27F3169B008B9AF6 /* MyAccount */,
				F3BF2AC227F469ED00DAD5B9 /* MyOrder */,
				036A2BBA28A21F520085AE87 /* Search */,
				F395CD9027F82D4F000E1852 /* Notification */,
				1A9649DE27F774F20048097C /* Cart */,
				F395CD8627F82656000E1852 /* Settings */,
				F3BF2A5F27F4476800DAD5B9 /* Address */,
				1A9177BE27F38B06003EA672 /* Payment */,
				1A9177B927F37F44003EA672 /* Login */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		18CC10D32AA3216F00C2C1E3 /* SuperBase */ = {
			isa = PBXGroup;
			children = (
				18CC10D62AA321D600C2C1E3 /* View */,
				18CC10D72AA3221100C2C1E3 /* SuperModel.swift */,
			);
			path = SuperBase;
			sourceTree = "<group>";
		};
		18CC10D62AA321D600C2C1E3 /* View */ = {
			isa = PBXGroup;
			children = (
				18CC10D42AA321B700C2C1E3 /* SuperView.swift */,
				18B4C4FE2AB0C5C00062735F /* NavigationView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18CC10D92AA3226F00C2C1E3 /* Components */ = {
			isa = PBXGroup;
			children = (
				18B4C4FA2AB0B90A0062735F /* Badge */,
				18CC10F12AA33B6400C2C1E3 /* BabyHouseImage */,
				18CC10EB2AA3331500C2C1E3 /* CustomDotsIndex */,
				F3BE500627EB4CCC00E3328A /* ProgressView */,
				18CC10E52AA323BF00C2C1E3 /* LottieAnimation */,
				18CC10DB2AA3228E00C2C1E3 /* AlertView */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		18CC10DB2AA3228E00C2C1E3 /* AlertView */ = {
			isa = PBXGroup;
			children = (
				18CC10DC2AA322AF00C2C1E3 /* AlertView.swift */,
			);
			path = AlertView;
			sourceTree = "<group>";
		};
		18CC10DE2AA322E900C2C1E3 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				18CC10DF2AA322F200C2C1E3 /* Utilities.swift */,
				F3BE4FF627EB4CCC00E3328A /* PickerField.swift */,
				F3BE4FF727EB4CCC00E3328A /* ShimmerView.swift */,
				F3BE4FF827EB4CCC00E3328A /* BaseViewModel.swift */,
				F3BE4FF927EB4CCC00E3328A /* Extensions.swift */,
				F3BE4FFA27EB4CCC00E3328A /* Storyboarded.swift */,
				F3BE4FFB27EB4CCC00E3328A /* Protocols.swift */,
				F3BE4FFC27EB4CCC00E3328A /* Badgebutton.swift */,
				F3BE4FFD27EB4CCC00E3328A /* ApplicationConfiguration.swift */,
				F3BE500527EB4CCC00E3328A /* Colourmanager.swift */,
				F3BE500C27EB4CCC00E3328A /* MActivityIndicator.swift */,
				F3BE4FFE27EB4CCC00E3328A /* Font */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		18CC10E12AA3237C00C2C1E3 /* Helper */ = {
			isa = PBXGroup;
			children = (
				18CC10EE2AA3388500C2C1E3 /* ViewPort */,
				F3BE502D27EB4D3400E3328A /* Network_manager */,
				18CC10E22AA3238200C2C1E3 /* ActivityLoader */,
				F30FDFFB27EEE84400674561 /* Localization */,
				18A3137B2E5081DF0036A202 /* APITestingGuide.swift */,
				18A3137C2E5081DF0036A202 /* AuthenticationLogger.swift */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		18CC10E22AA3238200C2C1E3 /* ActivityLoader */ = {
			isa = PBXGroup;
			children = (
				18CC10E32AA3238C00C2C1E3 /* ActivityLoader.swift */,
			);
			path = ActivityLoader;
			sourceTree = "<group>";
		};
		18CC10E52AA323BF00C2C1E3 /* LottieAnimation */ = {
			isa = PBXGroup;
			children = (
				18CC10E62AA323D800C2C1E3 /* LottieView.swift */,
			);
			path = LottieAnimation;
			sourceTree = "<group>";
		};
		18CC10E82AA3257000C2C1E3 /* Resources */ = {
			isa = PBXGroup;
			children = (
				18E980F62BB186BA00ACF8FD /* GoogleService-Info.plist */,
				F3BE4F4B27EB48A200E3328A /* Info.plist */,
				F3BE4FF027EB4CBA00E3328A /* LaunchScreen.storyboard */,
				F3BE4F4627EB48A200E3328A /* Assets.xcassets */,
				F3BE4F4327EB489D00E3328A /* BabyHouse.xcdatamodeld */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		18CC10E92AA3259100C2C1E3 /* Application */ = {
			isa = PBXGroup;
			children = (
				F3BE4F3A27EB489D00E3328A /* AppDelegate.swift */,
				F3BE4F3C27EB489D00E3328A /* SceneDelegate.swift */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		18CC10EA2AA326D800C2C1E3 /* Model */ = {
			isa = PBXGroup;
			children = (
				F3C4827E2800462A00C3047B /* HomeModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		18CC10EB2AA3331500C2C1E3 /* CustomDotsIndex */ = {
			isa = PBXGroup;
			children = (
				18CC10EC2AA3331F00C2C1E3 /* CustomDotsIndexView.swift */,
			);
			path = CustomDotsIndex;
			sourceTree = "<group>";
		};
		18CC10EE2AA3388500C2C1E3 /* ViewPort */ = {
			isa = PBXGroup;
			children = (
				18CC10EF2AA3389C00C2C1E3 /* ViewportHelper.swift */,
			);
			path = ViewPort;
			sourceTree = "<group>";
		};
		18CC10F12AA33B6400C2C1E3 /* BabyHouseImage */ = {
			isa = PBXGroup;
			children = (
				18CC10F22AA33B7C00C2C1E3 /* BabyHouseImageView.swift */,
			);
			path = BabyHouseImage;
			sourceTree = "<group>";
		};
		18CC10F92AA35AB900C2C1E3 /* Core */ = {
			isa = PBXGroup;
			children = (
				18CC10FA2AA35AC300C2C1E3 /* AppState */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		18CC10FA2AA35AC300C2C1E3 /* AppState */ = {
			isa = PBXGroup;
			children = (
				18CC10FB2AA35AD500C2C1E3 /* AppState.swift */,
			);
			path = AppState;
			sourceTree = "<group>";
		};
		18D2F5162AC43EF300F6BECD /* WebView */ = {
			isa = PBXGroup;
			children = (
				18BFCAC92AC2E9DB00B00222 /* WebContentView.swift */,
				18D2F5172AC43F0300F6BECD /* LoadingView.swift */,
				18D2F51D2AC43FB700F6BECD /* WebContainer.swift */,
				18D2F5192AC43F4000F6BECD /* WebView.swift */,
			);
			path = WebView;
			sourceTree = "<group>";
		};
		18EFC2E62AB973AA0029010D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			path = Frameworks;
			sourceTree = "<group>";
		};
		1A9177AA27F3752C003EA672 /* Categories */ = {
			isa = PBXGroup;
			children = (
				1A9177AF27F3754D003EA672 /* CategoryVM.swift */,
				1A9177B027F3754D003EA672 /* CategoryVC.swift */,
				1A9177AE27F3754D003EA672 /* Category.storyboard */,
				1A9177B127F3754D003EA672 /* CategoryTableViewCell.swift */,
				1A9177AD27F3754D003EA672 /* CategoryTableViewCell.xib */,
				1A9177AB27F3754D003EA672 /* TitleTableViewCell.swift */,
				1A9177AC27F3754D003EA672 /* TitleTableViewCell.xib */,
				03A1E4F628479EEE007B955A /* CategoryprodutsVC.swift */,
				03950D3B284F2E0700D7C19E /* CattitleCollectionViewCell.swift */,
				03950D3C284F2E0700D7C19E /* CattitleCollectionViewCell.xib */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		1A9177B927F37F44003EA672 /* Login */ = {
			isa = PBXGroup;
			children = (
				18BA514B2AAB378F002FEC90 /* custom_loader.json */,
				033301CC288144430037A7BD /* login.json */,
				1A9177BA27F37F55003EA672 /* Login.storyboard */,
				1A9649E327F776940048097C /* SignupVC.swift */,
				1A852A0028029B890074191B /* SignupVM.swift */,
				1A9177BC27F38917003EA672 /* LoginVC.swift */,
				18DC2ED72AEFE52E006E3D5D /* ResetPasswordVC.swift */,
				18E121CA2AECF9ED007D3C13 /* ForgetPasswordVC.swift */,
				18E121CC2AED1EEB007D3C13 /* VerifyOtpVC.swift */,
				1A958B1C28017F320088F84F /* LoginViewMode.swift */,
				18AD6D912ACC07AB00A2903B /* KeychainItem.swift */,
				182521752AEC12DC00402770 /* Otp */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		1A9177BE27F38B06003EA672 /* Payment */ = {
			isa = PBXGroup;
			children = (
				1A9177BF27F38B16003EA672 /* Payment.storyboard */,
			);
			path = Payment;
			sourceTree = "<group>";
		};
		1A9649DE27F774F20048097C /* Cart */ = {
			isa = PBXGroup;
			children = (
				1882ECC92AAF6FBE00186C87 /* Payment */,
				1A9649DF27F7750B0048097C /* Cart.storyboard */,
				1A9649E127F775760048097C /* OrderNoteVC.swift */,
				0332651C288698EB00927B09 /* Add_address */,
				1A9CDEFC27F9BB8C001E15E3 /* TermsVC.swift */,
				1A9CDF0227F9EBDE001E15E3 /* ShoppingBagVC.swift */,
				1AB40A1327FAB05B0060A933 /* CheckOutTotelVC.swift */,
				1AB40A1527FAB89E0060A933 /* PaymentVC.swift */,
				1A9CDEFF27F9CEFD001E15E3 /* CartTableViewCell.swift */,
				1A9CDEFE27F9CEFD001E15E3 /* CartTableViewCell.xib */,
				0314B71E28A2703E001E050E /* PaymentsuccessVC.swift */,
			);
			path = Cart;
			sourceTree = "<group>";
		};
		D89EA124C0B4F40F8FC9E41F /* Pods */ = {
			isa = PBXGroup;
			children = (
				94CCB6FC9E964BD509D68E01 /* Pods-BabyHouse.debug.xcconfig */,
				4E69950C7D98FA336A024384 /* Pods-BabyHouse.release.xcconfig */,
				C575EBD8C8249320B2E9C348 /* Pods-BabyHouse-BabyHouseUITests.debug.xcconfig */,
				161308BDF72684194B9B5679 /* Pods-BabyHouse-BabyHouseUITests.release.xcconfig */,
				C2583E2B309400F54A29014A /* Pods-BabyHouseTests.debug.xcconfig */,
				95FD843B48D334997175438C /* Pods-BabyHouseTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F30FDFE227EEE55500674561 /* Home */ = {
			isa = PBXGroup;
			children = (
				F33677332805405700896EC1 /* Detailpage */,
				F382C9F727F327FA008B9AF6 /* Cells */,
				F30FDFE827EEE63100674561 /* Menu */,
				F30FDFF127EEE63A00674561 /* HomecontainerVC.swift */,
				F30FDFE327EEE61D00674561 /* Home.storyboard */,
				F30FE01627EEEAEE00674561 /* HomeVM.swift */,
				1865DB7A2C7A3C8900C7C9D7 /* CarouselView.swift */,
				F30FE01127EEEAA100674561 /* HomeVC.swift */,
				F3C482832800503900C3047B /* Model.swift */,
				035FD1EF287AAF3C00910980 /* BlogVC.swift */,
				035FD1F1287AB0A600910980 /* BlogTableViewCell.swift */,
				035FD1F2287AB0A600910980 /* BlogTableViewCell.xib */,
				035FD1F5287AB55900910980 /* ProductListVC.swift */,
				18BA51512AAB5ECB002FEC90 /* SendEnquiryView.swift */,
				18BA51532AAB643D002FEC90 /* HomeViewModel.swift */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		F30FDFE827EEE63100674561 /* Menu */ = {
			isa = PBXGroup;
			children = (
				F30FDFE927EEE63100674561 /* SideinTransition.swift */,
				F30FDFEA27EEE63100674561 /* ProfileTableViewCell.swift */,
				F30FDFEB27EEE63100674561 /* ProfileTableViewCell.xib */,
				F30FDFEC27EEE63100674561 /* MenuVC.swift */,
			);
			path = Menu;
			sourceTree = "<group>";
		};
		F30FDFFB27EEE84400674561 /* Localization */ = {
			isa = PBXGroup;
			children = (
				F30FDFF627EEE83D00674561 /* LocalizationSystem.swift */,
				F30FE00927EEE8B200674561 /* Localizable.strings */,
			);
			path = Localization;
			sourceTree = "<group>";
		};
		F33677332805405700896EC1 /* Detailpage */ = {
			isa = PBXGroup;
			children = (
				18CC10EA2AA326D800C2C1E3 /* Model */,
				1871A1932AA0E94200978E37 /* View */,
				18CC10CF2AA31D7E00C2C1E3 /* ViewModel */,
			);
			path = Detailpage;
			sourceTree = "<group>";
		};
		F382C9EF27F3169B008B9AF6 /* MyAccount */ = {
			isa = PBXGroup;
			children = (
				18B4C4F12AB0B0DC0062735F /* LoyaltyCash */,
				18B4C4F32AB0B0EF0062735F /* Wallet */,
				18B4C4F22AB0B0E40062735F /* InviteFriends */,
				F382C9F027F3169B008B9AF6 /* MyAccountVC.swift */,
				F382C9F127F3169B008B9AF6 /* Account.storyboard */,
				033333F02850708500D95000 /* ChoosecurrencyVC.swift */,
				036D7B4E285773A50055F427 /* ChoosecountryVC.swift */,
			);
			path = MyAccount;
			sourceTree = "<group>";
		};
		F382C9F727F327FA008B9AF6 /* Cells */ = {
			isa = PBXGroup;
			children = (
				F38D263527FACF6F0066F9A0 /* Detailpage_Cells */,
				F382C9F827F32825008B9AF6 /* CategoryCollectionViewCell.swift */,
				F382C9F927F32825008B9AF6 /* CategoryCollectionViewCell.xib */,
				F382CA0227F32D7E008B9AF6 /* AddCollectionViewCell.swift */,
				F382CA0327F32D7E008B9AF6 /* AddCollectionViewCell.xib */,
				F382CA0927F331B0008B9AF6 /* newCollectionViewCell.swift */,
				F382CA0A27F331B0008B9AF6 /* newCollectionViewCell.xib */,
				F382CA1027F33BB6008B9AF6 /* add2CollectionViewCell.swift */,
				F382CA1127F33BB6008B9AF6 /* add2CollectionViewCell.xib */,
				F382CA1727F33D9A008B9AF6 /* FeatureCollectionViewCell.swift */,
				F382CA1827F33D9A008B9AF6 /* FeatureCollectionViewCell.xib */,
				F382CA2627F34E8D008B9AF6 /* BlogCollectionViewCell.swift */,
				F382CA2727F34E8D008B9AF6 /* BlogCollectionViewCell.xib */,
				18D09E542ACAC02900D3511B /* CollectionViewFlowLayout.swift */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		F38D263527FACF6F0066F9A0 /* Detailpage_Cells */ = {
			isa = PBXGroup;
			children = (
				F3EA880127F958D200D4EB13 /* ProductImgCollectionViewCell.swift */,
				F3EA880227F958D200D4EB13 /* ProductImgCollectionViewCell.xib */,
				F38D263E27FAD1580066F9A0 /* Size2CollectionViewCell.swift */,
				F38D263F27FAD1580066F9A0 /* Size2CollectionViewCell.xib */,
				F38D264B27FAD8330066F9A0 /* ColorCollectionViewCell.swift */,
				F38D264C27FAD8330066F9A0 /* ColorCollectionViewCell.xib */,
			);
			path = Detailpage_Cells;
			sourceTree = "<group>";
		};
		F395CD8627F82656000E1852 /* Settings */ = {
			isa = PBXGroup;
			children = (
				F395CD8727F82656000E1852 /* LanSelectVC.swift */,
				F395CD8827F82656000E1852 /* SettingsVC.swift */,
				F395CD8927F82656000E1852 /* Settings.storyboard */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		F395CD9027F82D4F000E1852 /* Notification */ = {
			isa = PBXGroup;
			children = (
				F395CD9127F82D4F000E1852 /* NotificationCell.xib */,
				F395CD9227F82D4F000E1852 /* NotificationCell.swift */,
				F395CD9327F82D4F000E1852 /* NotificationVC.swift */,
				F395CD9427F82D4F000E1852 /* NotificationViewModel.swift */,
			);
			path = Notification;
			sourceTree = "<group>";
		};
		F3BE4F2E27EB489D00E3328A = {
			isa = PBXGroup;
			children = (
				F3BE4F3927EB489D00E3328A /* BabyHouse */,
				F3BE4F5327EB48A200E3328A /* BabyHouseTests */,
				F3BE4F5E27EB48A200E3328A /* BabyHouseUITests */,
				F3BE4F3827EB489D00E3328A /* Products */,
				D89EA124C0B4F40F8FC9E41F /* Pods */,
				06546E23A31169FEE166F8F4 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		F3BE4F3827EB489D00E3328A /* Products */ = {
			isa = PBXGroup;
			children = (
				F3BE4F3727EB489D00E3328A /* BabyHouse.app */,
				F3BE4F5027EB48A200E3328A /* BabyHouseTests.xctest */,
				F3BE4F5B27EB48A200E3328A /* BabyHouseUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F3BE4F3927EB489D00E3328A /* BabyHouse */ = {
			isa = PBXGroup;
			children = (
				1849E1382AB8314700751BE3 /* BabyHouse.entitlements */,
				18CC10E92AA3259100C2C1E3 /* Application */,
				18CC10F92AA35AB900C2C1E3 /* Core */,
				18CC10D22AA3213F00C2C1E3 /* Module */,
				1871A18F2AA0DC0E00978E37 /* Global */,
				18CC10E82AA3257000C2C1E3 /* Resources */,
			);
			path = BabyHouse;
			sourceTree = "<group>";
		};
		F3BE4F5327EB48A200E3328A /* BabyHouseTests */ = {
			isa = PBXGroup;
			children = (
				F3BE4F5427EB48A200E3328A /* BabyHouseTests.swift */,
				F3BE4F5627EB48A200E3328A /* Info.plist */,
			);
			path = BabyHouseTests;
			sourceTree = "<group>";
		};
		F3BE4F5E27EB48A200E3328A /* BabyHouseUITests */ = {
			isa = PBXGroup;
			children = (
				F3BE4F5F27EB48A200E3328A /* BabyHouseUITests.swift */,
				F3BE4F6127EB48A200E3328A /* Info.plist */,
			);
			path = BabyHouseUITests;
			sourceTree = "<group>";
		};
		F3BE4FDD27EB4CAB00E3328A /* Splash */ = {
			isa = PBXGroup;
			children = (
				18BA514F2AAB3FA2002FEC90 /* splash_screen.json */,
				F3002A0528000F9B0059F532 /* AutheticationRequest.swift */,
				F3BE4FE027EB4CAB00E3328A /* Getstarted */,
				F3BE4FE527EB4CAB00E3328A /* SplashView.swift */,
				F3BE4FE627EB4CAB00E3328A /* Splash.storyboard */,
			);
			path = Splash;
			sourceTree = "<group>";
		};
		F3BE4FE027EB4CAB00E3328A /* Getstarted */ = {
			isa = PBXGroup;
			children = (
				F3BE4FE127EB4CAB00E3328A /* Cell */,
				F3BE4FE427EB4CAB00E3328A /* GetstartVC.swift */,
			);
			path = Getstarted;
			sourceTree = "<group>";
		};
		F3BE4FE127EB4CAB00E3328A /* Cell */ = {
			isa = PBXGroup;
			children = (
				F3BE4FE227EB4CAB00E3328A /* StartCollectionViewCell.xib */,
				F3BE4FE327EB4CAB00E3328A /* StartCollectionViewCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		F3BE4FFE27EB4CCC00E3328A /* Font */ = {
			isa = PBXGroup;
			children = (
				0327024A288287040010AEF8 /* Metropolis-Medium.otf */,
				03270250288287290010AEF8 /* Metropolis-Black.otf */,
				0327024C288287130010AEF8 /* Metropolis-Regular.otf */,
				03270252288287340010AEF8 /* Metropolis-Light.otf */,
				18B6968C2AB1A71F00879BA6 /* Metropolis-SemiBold.otf */,
				0327024E2882871F0010AEF8 /* Metropolis-Bold.otf */,
				F3BE4FFF27EB4CCC00E3328A /* Roboto-Medium.ttf */,
				F3BE500027EB4CCC00E3328A /* Roboto-Light.ttf */,
				F3BE500127EB4CCC00E3328A /* Roboto-Regular.ttf */,
				F3BE500227EB4CCC00E3328A /* Fonts.swift */,
				F3BE500327EB4CCC00E3328A /* Roboto-Bold.ttf */,
				F3BE500427EB4CCC00E3328A /* Roboto-Black.ttf */,
			);
			path = Font;
			sourceTree = "<group>";
		};
		F3BE500627EB4CCC00E3328A /* ProgressView */ = {
			isa = PBXGroup;
			children = (
				F3BE500727EB4CCC00E3328A /* StrokeColorAnimation.swift */,
				F3BE500827EB4CCC00E3328A /* RotationAnimation.swift */,
				F3BE500927EB4CCC00E3328A /* ProgressView.swift */,
				F3BE500A27EB4CCC00E3328A /* StrokeAnimation.swift */,
				F3BE500B27EB4CCC00E3328A /* ProgressShapeLayer.swift */,
			);
			path = ProgressView;
			sourceTree = "<group>";
		};
		F3BE502D27EB4D3400E3328A /* Network_manager */ = {
			isa = PBXGroup;
			children = (
				F3BE502E27EB4D3400E3328A /* LightNetwork */,
				F3BE503427EB4D3500E3328A /* App.swift */,
			);
			path = Network_manager;
			sourceTree = "<group>";
		};
		F3BE502E27EB4D3400E3328A /* LightNetwork */ = {
			isa = PBXGroup;
			children = (
				F3BE502F27EB4D3400E3328A /* NError.swift */,
				F3BE503027EB4D3400E3328A /* Helpers.swift */,
				F3BE503127EB4D3400E3328A /* Network.swift */,
				F3BE503227EB4D3400E3328A /* Reachability.swift */,
				F3BE503327EB4D3400E3328A /* Endpoint.swift */,
			);
			path = LightNetwork;
			sourceTree = "<group>";
		};
		F3BF2A3327F4161500DAD5B9 /* Profile */ = {
			isa = PBXGroup;
			children = (
				F3BF2A3E27F416AC00DAD5B9 /* EditProfileViewModel.swift */,
				F3BF2A3427F4162800DAD5B9 /* Profile.storyboard */,
				F3BF2A4427F4230100DAD5B9 /* ChangePasswordVM.swift */,
				F3BF2A4327F4230100DAD5B9 /* ChangepwdVC.swift */,
				F3BF2A3927F4164100DAD5B9 /* EditProfileVC.swift */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		F3BF2A4A27F4292000DAD5B9 /* WishList */ = {
			isa = PBXGroup;
			children = (
				F3BF2A4B27F4292000DAD5B9 /* WishListVC.swift */,
				F3BF2A4C27F4292000DAD5B9 /* Cells */,
				F3BF2A4F27F4292000DAD5B9 /* Wishlist.storyboard */,
				F3BF2A5027F4292000DAD5B9 /* WishlistModel.swift */,
			);
			path = WishList;
			sourceTree = "<group>";
		};
		F3BF2A4C27F4292000DAD5B9 /* Cells */ = {
			isa = PBXGroup;
			children = (
				F3BF2A4D27F4292000DAD5B9 /* WishListCell.xib */,
				F3BF2A4E27F4292000DAD5B9 /* WishListCell.swift */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		F3BF2A5F27F4476800DAD5B9 /* Address */ = {
			isa = PBXGroup;
			children = (
				F3BF2A6127F4476800DAD5B9 /* Cells */,
				F3BF2A6527F4476800DAD5B9 /* AddressVC.swift */,
				F3BF2A6627F4476800DAD5B9 /* Address.storyboard */,
				1A8A17D5285F9B1D0089CE04 /* AddressVM.swift */,
				1A8A17D7285F9B620089CE04 /* AddressRequest.swift */,
			);
			path = Address;
			sourceTree = "<group>";
		};
		F3BF2A6127F4476800DAD5B9 /* Cells */ = {
			isa = PBXGroup;
			children = (
				F3BF2A6227F4476800DAD5B9 /* AddressListCell.swift */,
				F3BF2A6327F4476800DAD5B9 /* AddressListCell.xib */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		F3BF2AC227F469ED00DAD5B9 /* MyOrder */ = {
			isa = PBXGroup;
			children = (
				18B6968E2AB1C44400879BA6 /* OrderItems */,
				F395CD8127F81A52000E1852 /* Order.storyboard */,
				F3BF2AC427F469ED00DAD5B9 /* MyOrderVC.swift */,
				18B696952AB1C53900879BA6 /* MyOrderListVC.swift */,
				1A8A17D1285F8A4B0089CE04 /* MyOrderVM.swift */,
				F3BF2AC527F469ED00DAD5B9 /* RateUsVC.swift */,
				F3BF2AC627F469ED00DAD5B9 /* TrackorderVC.swift */,
				F3BF2AC827F469ED00DAD5B9 /* RatingVC.swift */,
				F3BF2AC927F469ED00DAD5B9 /* MyOrdersListCell.swift */,
				F3BF2AC327F469ED00DAD5B9 /* MyOrdersListCell.xib */,
				1A8A17D3285F8B260089CE04 /* OrderRequest.swift */,
				0314B71A28A2661B001E050E /* TrackTableViewCell.swift */,
				0314B71B28A2661B001E050E /* TrackTableViewCell.xib */,
			);
			path = MyOrder;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F3BE4F3627EB489D00E3328A /* BabyHouse */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3BE4F6427EB48A200E3328A /* Build configuration list for PBXNativeTarget "BabyHouse" */;
			buildPhases = (
				8C0BAEB599AE8C1FEAEBCAD3 /* [CP] Check Pods Manifest.lock */,
				F3BE4F3327EB489D00E3328A /* Sources */,
				F3BE4F3427EB489D00E3328A /* Frameworks */,
				F3BE4F3527EB489D00E3328A /* Resources */,
				82F0B12007AF02EC65E0B201 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = BabyHouse;
			packageProductDependencies = (
				18CC10F52AA33BA800C2C1E3 /* SDWebImageSwiftUI */,
				18D8E65E2AA5D43900189C95 /* RichText */,
				18BA51482AAAFA88002FEC90 /* WrappingHStack */,
				1849E13C2AB8491300751BE3 /* Siren */,
				18D2F5142AC411A300F6BECD /* SwiftyJSON */,
				18AD6D972ACC13F100A2903B /* GoogleSignIn */,
				18AD6D992ACC13F100A2903B /* GoogleSignInSwift */,
				18AD6D9C2ACC179500A2903B /* FirebaseAuth */,
				182521712AEBC5C000402770 /* FirebaseMessaging */,
				1865DB782C7A351B00C7C9D7 /* InfiniteCarousel */,
				1865DB802C7A4CD600C7C9D7 /* SwiftUIInfiniteCarousel */,
			);
			productName = BabyHouse;
			productReference = F3BE4F3727EB489D00E3328A /* BabyHouse.app */;
			productType = "com.apple.product-type.application";
		};
		F3BE4F4F27EB48A200E3328A /* BabyHouseTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3BE4F6727EB48A200E3328A /* Build configuration list for PBXNativeTarget "BabyHouseTests" */;
			buildPhases = (
				3AA72D0764CBCF393183A98B /* [CP] Check Pods Manifest.lock */,
				F3BE4F4C27EB48A200E3328A /* Sources */,
				F3BE4F4D27EB48A200E3328A /* Frameworks */,
				F3BE4F4E27EB48A200E3328A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F3BE4F5227EB48A200E3328A /* PBXTargetDependency */,
			);
			name = BabyHouseTests;
			productName = BabyHouseTests;
			productReference = F3BE4F5027EB48A200E3328A /* BabyHouseTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F3BE4F5A27EB48A200E3328A /* BabyHouseUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3BE4F6A27EB48A200E3328A /* Build configuration list for PBXNativeTarget "BabyHouseUITests" */;
			buildPhases = (
				BE63A2F7718555F54800B2D7 /* [CP] Check Pods Manifest.lock */,
				F3BE4F5727EB48A200E3328A /* Sources */,
				F3BE4F5827EB48A200E3328A /* Frameworks */,
				F3BE4F5927EB48A200E3328A /* Resources */,
				0E558A3B92BB536CFA3F57CC /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				F3BE4F5D27EB48A200E3328A /* PBXTargetDependency */,
			);
			name = BabyHouseUITests;
			productName = BabyHouseUITests;
			productReference = F3BE4F5B27EB48A200E3328A /* BabyHouseUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F3BE4F2F27EB489D00E3328A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1240;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					F3BE4F3627EB489D00E3328A = {
						CreatedOnToolsVersion = 12.4;
						LastSwiftMigration = 1330;
					};
					F3BE4F4F27EB48A200E3328A = {
						CreatedOnToolsVersion = 12.4;
						TestTargetID = F3BE4F3627EB489D00E3328A;
					};
					F3BE4F5A27EB48A200E3328A = {
						CreatedOnToolsVersion = 12.4;
						TestTargetID = F3BE4F3627EB489D00E3328A;
					};
				};
			};
			buildConfigurationList = F3BE4F3227EB489D00E3328A /* Build configuration list for PBXProject "BabyHouse" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
			);
			mainGroup = F3BE4F2E27EB489D00E3328A;
			packageReferences = (
				18CC10F42AA33BA800C2C1E3 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				18D8E65D2AA5D43900189C95 /* XCRemoteSwiftPackageReference "RichText" */,
				18BA51472AAAFA88002FEC90 /* XCRemoteSwiftPackageReference "WrappingHStack" */,
				1849E13B2AB8491300751BE3 /* XCRemoteSwiftPackageReference "Siren" */,
				18D2F5132AC411A300F6BECD /* XCRemoteSwiftPackageReference "SwiftyJSON" */,
				18AD6D962ACC13F100A2903B /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				18AD6D9B2ACC179400A2903B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				1865DB772C7A351B00C7C9D7 /* XCLocalSwiftPackageReference "InfiniteCarousel-master" */,
				1865DB7F2C7A4CD600C7C9D7 /* XCLocalSwiftPackageReference "SwiftUI-Infinite-Carousel-main" */,
			);
			productRefGroup = F3BE4F3827EB489D00E3328A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F3BE4F3627EB489D00E3328A /* BabyHouse */,
				F3BE4F4F27EB48A200E3328A /* BabyHouseTests */,
				F3BE4F5A27EB48A200E3328A /* BabyHouseUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F3BE4F3527EB489D00E3328A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F33677372805408500896EC1 /* AttributeTableViewCell.xib in Resources */,
				F382CA0527F32D7E008B9AF6 /* AddCollectionViewCell.xib in Resources */,
				03950D3E284F2E0700D7C19E /* CattitleCollectionViewCell.xib in Resources */,
				18BA514C2AAB378F002FEC90 /* custom_loader.json in Resources */,
				F3BE501527EB4CCC00E3328A /* Roboto-Medium.ttf in Resources */,
				F38D264E27FAD8330066F9A0 /* ColorCollectionViewCell.xib in Resources */,
				F3BE501727EB4CCC00E3328A /* Roboto-Regular.ttf in Resources */,
				1A9177B427F3754D003EA672 /* CategoryTableViewCell.xib in Resources */,
				F3BF2A6F27F4476900DAD5B9 /* Address.storyboard in Resources */,
				1A9177B327F3754D003EA672 /* TitleTableViewCell.xib in Resources */,
				F30FDFE427EEE61D00674561 /* Home.storyboard in Resources */,
				F30FE00727EEE8B200674561 /* Localizable.strings in Resources */,
				035FD1F4287AB0A600910980 /* BlogTableViewCell.xib in Resources */,
				F30FDFEF27EEE63100674561 /* ProfileTableViewCell.xib in Resources */,
				F3BF2A3527F4162800DAD5B9 /* Profile.storyboard in Resources */,
				1A9177C027F38B16003EA672 /* Payment.storyboard in Resources */,
				F382C9F327F3169B008B9AF6 /* Account.storyboard in Resources */,
				1A9177B527F3754D003EA672 /* Category.storyboard in Resources */,
				18BA514A2AAB3763002FEC90 /* login.json in Resources */,
				F382CA1A27F33D9A008B9AF6 /* FeatureCollectionViewCell.xib in Resources */,
				0327024D288287130010AEF8 /* Metropolis-Regular.otf in Resources */,
				1A9177BB27F37F55003EA672 /* Login.storyboard in Resources */,
				F3BE4FF127EB4CBA00E3328A /* LaunchScreen.storyboard in Resources */,
				F3BE501627EB4CCC00E3328A /* Roboto-Light.ttf in Resources */,
				18BA51502AAB3FA2002FEC90 /* splash_screen.json in Resources */,
				1A9CDF0027F9CEFD001E15E3 /* CartTableViewCell.xib in Resources */,
				036A2BCB28A222020085AE87 /* Search.storyboard in Resources */,
				F395CD8227F81A52000E1852 /* Order.storyboard in Resources */,
				F3C6F99427ECB6E60060C854 /* (null) in Resources */,
				036A2BC928A2211F0085AE87 /* ProductsearchTableViewCell.xib in Resources */,
				F38D264127FAD1580066F9A0 /* Size2CollectionViewCell.xib in Resources */,
				F3BF2A6C27F4476900DAD5B9 /* AddressListCell.xib in Resources */,
				F395CD9527F82D4F000E1852 /* NotificationCell.xib in Resources */,
				18E980F72BB186BA00ACF8FD /* GoogleService-Info.plist in Resources */,
				F3BE4FE827EB4CAB00E3328A /* StartCollectionViewCell.xib in Resources */,
				18B6968D2AB1A71F00879BA6 /* Metropolis-SemiBold.otf in Resources */,
				0327024B288287040010AEF8 /* Metropolis-Medium.otf in Resources */,
				F3BE501A27EB4CCC00E3328A /* Roboto-Black.ttf in Resources */,
				1A9649E027F7750B0048097C /* Cart.storyboard in Resources */,
				0327024F2882871F0010AEF8 /* Metropolis-Bold.otf in Resources */,
				0358F0452887CEDC00A8CF80 /* AreaTableViewCell.xib in Resources */,
				F382CA2927F34E8D008B9AF6 /* BlogCollectionViewCell.xib in Resources */,
				F3BF2ACA27F469ED00DAD5B9 /* MyOrdersListCell.xib in Resources */,
				03270251288287290010AEF8 /* Metropolis-Black.otf in Resources */,
				03270253288287340010AEF8 /* Metropolis-Light.otf in Resources */,
				F3BF2A5227F4292000DAD5B9 /* WishListCell.xib in Resources */,
				F382CA1327F33BB6008B9AF6 /* add2CollectionViewCell.xib in Resources */,
				F382CA0C27F331B0008B9AF6 /* newCollectionViewCell.xib in Resources */,
				F3BE4F4727EB48A200E3328A /* Assets.xcassets in Resources */,
				F3BF2A5427F4292000DAD5B9 /* Wishlist.storyboard in Resources */,
				F3BE501927EB4CCC00E3328A /* Roboto-Bold.ttf in Resources */,
				F382C9FB27F32825008B9AF6 /* CategoryCollectionViewCell.xib in Resources */,
				F3C6F99827ECB6E60060C854 /* (null) in Resources */,
				F3EA880427F958D200D4EB13 /* ProductImgCollectionViewCell.xib in Resources */,
				033265202886990900927B09 /* TagCollectionViewCell.xib in Resources */,
				0314B71D28A2661B001E050E /* TrackTableViewCell.xib in Resources */,
				F3BE4FEC27EB4CAB00E3328A /* Splash.storyboard in Resources */,
				F395CD8C27F82656000E1852 /* Settings.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BE4F4E27EB48A200E3328A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BE4F5927EB48A200E3328A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0E558A3B92BB536CFA3F57CC /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-BabyHouse-BabyHouseUITests/Pods-BabyHouse-BabyHouseUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-BabyHouse-BabyHouseUITests/Pods-BabyHouse-BabyHouseUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-BabyHouse-BabyHouseUITests/Pods-BabyHouse-BabyHouseUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3AA72D0764CBCF393183A98B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-BabyHouseTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		82F0B12007AF02EC65E0B201 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-BabyHouse/Pods-BabyHouse-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-BabyHouse/Pods-BabyHouse-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-BabyHouse/Pods-BabyHouse-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8C0BAEB599AE8C1FEAEBCAD3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-BabyHouse-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		BE63A2F7718555F54800B2D7 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-BabyHouse-BabyHouseUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F3BE4F3327EB489D00E3328A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F3BF2AD027F469ED00DAD5B9 /* MyOrdersListCell.swift in Sources */,
				F382C9F227F3169B008B9AF6 /* MyAccountVC.swift in Sources */,
				036A2BC828A2211F0085AE87 /* ProductsearchTableViewCell.swift in Sources */,
				F3BE503727EB4D3500E3328A /* Network.swift in Sources */,
				F382CA0427F32D7E008B9AF6 /* AddCollectionViewCell.swift in Sources */,
				1871A1952AA0E95D00978E37 /* ProductDetailView.swift in Sources */,
				18BFCACA2AC2E9DB00B00222 /* WebContentView.swift in Sources */,
				F3BE4FE927EB4CAB00E3328A /* StartCollectionViewCell.swift in Sources */,
				1A958B1D28017F320088F84F /* LoginViewMode.swift in Sources */,
				18BA51522AAB5ECB002FEC90 /* SendEnquiryView.swift in Sources */,
				F3C482842800503900C3047B /* Model.swift in Sources */,
				F3BE501F27EB4CCC00E3328A /* StrokeAnimation.swift in Sources */,
				18DC2ED82AEFE52E006E3D5D /* ResetPasswordVC.swift in Sources */,
				F3BE503A27EB4D3500E3328A /* App.swift in Sources */,
				18B696902AB1C45900879BA6 /* OrderListView.swift in Sources */,
				1882ECCF2AAF969A00186C87 /* PaymentModel.swift in Sources */,
				18CC10D12AA31D9C00C2C1E3 /* ProductDetailViewModel.swift in Sources */,
				18B4C4F52AB0B1210062735F /* LoyaltyCashView.swift in Sources */,
				F3BE501B27EB4CCC00E3328A /* Colourmanager.swift in Sources */,
				F3BF2A6E27F4476900DAD5B9 /* AddressVC.swift in Sources */,
				03A1E4F728479EEE007B955A /* CategoryprodutsVC.swift in Sources */,
				033333F12850708500D95000 /* ChoosecurrencyVC.swift in Sources */,
				18CC10DD2AA322AF00C2C1E3 /* AlertView.swift in Sources */,
				F382CA1927F33D9A008B9AF6 /* FeatureCollectionViewCell.swift in Sources */,
				F3C6F99127ECB6E60060C854 /* (null) in Sources */,
				F3C6F99227ECB6E60060C854 /* (null) in Sources */,
				F3BF2ACF27F469ED00DAD5B9 /* RatingVC.swift in Sources */,
				18E121CB2AECF9ED007D3C13 /* ForgetPasswordVC.swift in Sources */,
				0332651F2886990900927B09 /* TagCollectionViewCell.swift in Sources */,
				F30FDFF727EEE83D00674561 /* LocalizationSystem.swift in Sources */,
				F30FDFEE27EEE63100674561 /* ProfileTableViewCell.swift in Sources */,
				18CC10F32AA33B7C00C2C1E3 /* BabyHouseImageView.swift in Sources */,
				1871A1972AA0F82200978E37 /* ViewWrapper.swift in Sources */,
				182521742AEC066800402770 /* PromoListView.swift in Sources */,
				1871A1922AA0DC2B00978E37 /* Notification.swift in Sources */,
				035FD1F6287AB55900910980 /* ProductListVC.swift in Sources */,
				F3BE500D27EB4CCC00E3328A /* PickerField.swift in Sources */,
				1A8A17D6285F9B1D0089CE04 /* AddressVM.swift in Sources */,
				1A9CDEF727F9A65A001E15E3 /* AddAddressVC.swift in Sources */,
				F3BE503827EB4D3500E3328A /* Reachability.swift in Sources */,
				033265222886C11A00927B09 /* MapVC.swift in Sources */,
				18B6969E2AB1D36500879BA6 /* LoyaltyCashModel.swift in Sources */,
				F3C6F99727ECB6E60060C854 /* (null) in Sources */,
				F3BE501127EB4CCC00E3328A /* Storyboarded.swift in Sources */,
				18B696922AB1C48E00879BA6 /* OrderListViewModel.swift in Sources */,
				18CBE8802AB5C79F00B5BF57 /* ShareEarnModel.swift in Sources */,
				F382CA2827F34E8D008B9AF6 /* BlogCollectionViewCell.swift in Sources */,
				0358F0412887CE9A00A8CF80 /* AreaVC.swift in Sources */,
				F3BF2ACD27F469ED00DAD5B9 /* TrackorderVC.swift in Sources */,
				F3BE500F27EB4CCC00E3328A /* BaseViewModel.swift in Sources */,
				F3BF2A6B27F4476900DAD5B9 /* AddressListCell.swift in Sources */,
				18B6969A2AB1D34200879BA6 /* WalletModel.swift in Sources */,
				F33677362805408500896EC1 /* AttributeTableViewCell.swift in Sources */,
				F3BE501D27EB4CCC00E3328A /* RotationAnimation.swift in Sources */,
				18B6969C2AB1D35C00879BA6 /* LoyaltyCashViewModel.swift in Sources */,
				0358F0442887CEDC00A8CF80 /* AreaTableViewCell.swift in Sources */,
				18CC10E42AA3238C00C2C1E3 /* ActivityLoader.swift in Sources */,
				F3EA880327F958D200D4EB13 /* ProductImgCollectionViewCell.swift in Sources */,
				1882ECD32AAF9E9300186C87 /* PaymentViewModel.swift in Sources */,
				1A9177B727F3754D003EA672 /* CategoryVC.swift in Sources */,
				1865DB7B2C7A3C8900C7C9D7 /* CarouselView.swift in Sources */,
				F30FE01227EEEAA100674561 /* HomeVC.swift in Sources */,
				0314B71C28A2661B001E050E /* TrackTableViewCell.swift in Sources */,
				1A9CDEFD27F9BB8C001E15E3 /* TermsVC.swift in Sources */,
				1882ECCD2AAF6FF700186C87 /* PaymentView.swift in Sources */,
				1825217A2AEC131600402770 /* OtpView.swift in Sources */,
				F3BE4F4527EB489D00E3328A /* BabyHouse.xcdatamodeld in Sources */,
				F3BE501827EB4CCC00E3328A /* Fonts.swift in Sources */,
				F3BF2ACB27F469ED00DAD5B9 /* MyOrderVC.swift in Sources */,
				18CC10F02AA3389C00C2C1E3 /* ViewportHelper.swift in Sources */,
				18B4C4FF2AB0C5C00062735F /* NavigationView.swift in Sources */,
				1A9649E227F775760048097C /* OrderNoteVC.swift in Sources */,
				1825217C2AEC134500402770 /* OtpViewModel.swift in Sources */,
				18CC10D52AA321B700C2C1E3 /* SuperView.swift in Sources */,
				F3BF2A3A27F4164100DAD5B9 /* EditProfileVC.swift in Sources */,
				F395CD9727F82D4F000E1852 /* NotificationVC.swift in Sources */,
				18B6968B2AB1A67900879BA6 /* Font.swift in Sources */,
				1A9177B227F3754D003EA672 /* TitleTableViewCell.swift in Sources */,
				F3BF2ACC27F469ED00DAD5B9 /* RateUsVC.swift in Sources */,
				1A8A17D4285F8B260089CE04 /* OrderRequest.swift in Sources */,
				18B696982AB1D33900879BA6 /* WalletViewModel.swift in Sources */,
				F3C6F99A27ECB6E60060C854 /* (null) in Sources */,
				F395CD9627F82D4F000E1852 /* NotificationCell.swift in Sources */,
				1A9CDF0127F9CEFD001E15E3 /* CartTableViewCell.swift in Sources */,
				F3BE4FEA27EB4CAB00E3328A /* GetstartVC.swift in Sources */,
				1A8A17D2285F8A4B0089CE04 /* MyOrderVM.swift in Sources */,
				18AD6D922ACC07AB00A2903B /* KeychainItem.swift in Sources */,
				F395CD9827F82D4F000E1852 /* NotificationViewModel.swift in Sources */,
				F3BE501427EB4CCC00E3328A /* ApplicationConfiguration.swift in Sources */,
				F382CA0B27F331B0008B9AF6 /* newCollectionViewCell.swift in Sources */,
				18CC10F82AA33CD400C2C1E3 /* View.swift in Sources */,
				18B4C4F82AB0B2A10062735F /* InviteFriendsView.swift in Sources */,
				F395CD8B27F82656000E1852 /* SettingsVC.swift in Sources */,
				F3BE502027EB4CCC00E3328A /* ProgressShapeLayer.swift in Sources */,
				F3BE503927EB4D3500E3328A /* Endpoint.swift in Sources */,
				18BA51542AAB643D002FEC90 /* HomeViewModel.swift in Sources */,
				18B4C4F92AB0B2AB0062735F /* WalletView.swift in Sources */,
				1A852A0128029B8A0074191B /* SignupVM.swift in Sources */,
				1A9CDF0327F9EBDE001E15E3 /* ShoppingBagVC.swift in Sources */,
				18B696942AB1C4B300879BA6 /* OrderListModel.swift in Sources */,
				18D2F5182AC43F0300F6BECD /* LoadingView.swift in Sources */,
				035FD1F0287AAF3C00910980 /* BlogVC.swift in Sources */,
				F3BE501227EB4CCC00E3328A /* Protocols.swift in Sources */,
				F395CD8A27F82656000E1852 /* LanSelectVC.swift in Sources */,
				F3BF2A4527F4230100DAD5B9 /* ChangepwdVC.swift in Sources */,
				F3C6F99527ECB6E60060C854 /* (null) in Sources */,
				F3BE503627EB4D3500E3328A /* Helpers.swift in Sources */,
				18CBE8822AB5C82800B5BF57 /* InviteFriendsViewModel.swift in Sources */,
				F3BE4F3B27EB489D00E3328A /* AppDelegate.swift in Sources */,
				F3BE503527EB4D3500E3328A /* NError.swift in Sources */,
				18CC10E02AA322F200C2C1E3 /* Utilities.swift in Sources */,
				F3BF2A5127F4292000DAD5B9 /* WishListVC.swift in Sources */,
				036D7B4F285773A50055F427 /* ChoosecountryVC.swift in Sources */,
				18E121CD2AED1EEB007D3C13 /* VerifyOtpVC.swift in Sources */,
				F3BF2A5327F4292000DAD5B9 /* WishListCell.swift in Sources */,
				18CC10FC2AA35AD500C2C1E3 /* AppState.swift in Sources */,
				F30FE01727EEEAEE00674561 /* HomeVM.swift in Sources */,
				F3BE501327EB4CCC00E3328A /* Badgebutton.swift in Sources */,
				18CC10E72AA323D800C2C1E3 /* LottieView.swift in Sources */,
				F3BF2A4627F4230100DAD5B9 /* ChangePasswordVM.swift in Sources */,
				18D2F51A2AC43F4000F6BECD /* WebView.swift in Sources */,
				1A9649E427F776940048097C /* SignupVC.swift in Sources */,
				1AB40A1627FAB89E0060A933 /* PaymentVC.swift in Sources */,
				F382C9FA27F32825008B9AF6 /* CategoryCollectionViewCell.swift in Sources */,
				F3BE500E27EB4CCC00E3328A /* ShimmerView.swift in Sources */,
				F3C6F99927ECB6E60060C854 /* (null) in Sources */,
				F30FDFF227EEE63A00674561 /* HomecontainerVC.swift in Sources */,
				18CC10D82AA3221100C2C1E3 /* SuperModel.swift in Sources */,
				F3BF2A3F27F416AC00DAD5B9 /* EditProfileViewModel.swift in Sources */,
				036A2BC128A21F750085AE87 /* SearchModel.swift in Sources */,
				F38D264027FAD1580066F9A0 /* Size2CollectionViewCell.swift in Sources */,
				18CC10FE2AA3672C00C2C1E3 /* Color.swift in Sources */,
				036A2BBC28A21F5F0085AE87 /* SearchVC.swift in Sources */,
				F3BE501C27EB4CCC00E3328A /* StrokeColorAnimation.swift in Sources */,
				F3BE501027EB4CCC00E3328A /* Extensions.swift in Sources */,
				F3C4827F2800462B00C3047B /* HomeModel.swift in Sources */,
				F3C6F99627ECB6E60060C854 /* (null) in Sources */,
				F3EA87FB27F9577F00D4EB13 /* ProductdetailVC.swift in Sources */,
				1A9177BD27F38917003EA672 /* LoginVC.swift in Sources */,
				1AB40A1427FAB05B0060A933 /* CheckOutTotelVC.swift in Sources */,
				18EFC2E82AB9BE0B0029010D /* GovernorateVC.swift in Sources */,
				03950D3D284F2E0700D7C19E /* CattitleCollectionViewCell.swift in Sources */,
				18B4C4FC2AB0B91D0062735F /* BadgeView.swift in Sources */,
				F30FDFF027EEE63100674561 /* MenuVC.swift in Sources */,
				0314B71F28A2703E001E050E /* PaymentsuccessVC.swift in Sources */,
				F3BF2A5527F4292000DAD5B9 /* WishlistModel.swift in Sources */,
				1A9177B627F3754D003EA672 /* CategoryVM.swift in Sources */,
				18CC10ED2AA3331F00C2C1E3 /* CustomDotsIndexView.swift in Sources */,
				18D09E552ACAC02900D3511B /* CollectionViewFlowLayout.swift in Sources */,
				F382CA1227F33BB6008B9AF6 /* add2CollectionViewCell.swift in Sources */,
				1A8A17D8285F9B620089CE04 /* AddressRequest.swift in Sources */,
				F3BE4FEB27EB4CAB00E3328A /* SplashView.swift in Sources */,
				F3BE4F3D27EB489D00E3328A /* SceneDelegate.swift in Sources */,
				F38D264D27FAD8330066F9A0 /* ColorCollectionViewCell.swift in Sources */,
				F3C6F99327ECB6E60060C854 /* (null) in Sources */,
				F3002A0628000FA50059F532 /* AutheticationRequest.swift in Sources */,
				F30FDFED27EEE63100674561 /* SideinTransition.swift in Sources */,
				18B696962AB1C53900879BA6 /* MyOrderListVC.swift in Sources */,
				F3BE502127EB4CCC00E3328A /* MActivityIndicator.swift in Sources */,
				18A3137D2E5081DF0036A202 /* AuthenticationLogger.swift in Sources */,
				18A3137E2E5081DF0036A202 /* APITestingGuide.swift in Sources */,
				F3BE501E27EB4CCC00E3328A /* ProgressView.swift in Sources */,
				1A9177B827F3754D003EA672 /* CategoryTableViewCell.swift in Sources */,
				035FD1F3287AB0A600910980 /* BlogTableViewCell.swift in Sources */,
				18D2F51E2AC43FB700F6BECD /* WebContainer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BE4F4C27EB48A200E3328A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F3BE4F5527EB48A200E3328A /* BabyHouseTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BE4F5727EB48A200E3328A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F3BE4F6027EB48A200E3328A /* BabyHouseUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F3BE4F5227EB48A200E3328A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F3BE4F3627EB489D00E3328A /* BabyHouse */;
			targetProxy = F3BE4F5127EB48A200E3328A /* PBXContainerItemProxy */;
		};
		F3BE4F5D27EB48A200E3328A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F3BE4F3627EB489D00E3328A /* BabyHouse */;
			targetProxy = F3BE4F5C27EB48A200E3328A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		F30FE00927EEE8B200674561 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				F30FE00827EEE8B200674561 /* en */,
				F30FE00D27EEE8B600674561 /* ar */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		F3BE4F6227EB48A200E3328A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F3BE4F6327EB48A200E3328A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F3BE4F6527EB48A200E3328A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 94CCB6FC9E964BD509D68E01 /* Pods-BabyHouse.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = BabyHouse/BabyHouse.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9B444SP62L;
				INFOPLIST_FILE = BabyHouse/Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Baby House Toys";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.5;
				PRODUCT_BUNDLE_IDENTIFIER = com.babyhouse.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		F3BE4F6627EB48A200E3328A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4E69950C7D98FA336A024384 /* Pods-BabyHouse.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = BabyHouse/BabyHouse.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9B444SP62L;
				INFOPLIST_FILE = BabyHouse/Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Baby House Toys";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.5;
				PRODUCT_BUNDLE_IDENTIFIER = com.babyhouse.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		F3BE4F6827EB48A200E3328A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C2583E2B309400F54A29014A /* Pods-BabyHouseTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = BabyHouseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = sub91.BabyHouseTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BabyHouse.app/BabyHouse";
			};
			name = Debug;
		};
		F3BE4F6927EB48A200E3328A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 95FD843B48D334997175438C /* Pods-BabyHouseTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = BabyHouseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = sub91.BabyHouseTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BabyHouse.app/BabyHouse";
			};
			name = Release;
		};
		F3BE4F6B27EB48A200E3328A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C575EBD8C8249320B2E9C348 /* Pods-BabyHouse-BabyHouseUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = BabyHouseUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = sub91.BabyHouseUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = BabyHouse;
			};
			name = Debug;
		};
		F3BE4F6C27EB48A200E3328A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 161308BDF72684194B9B5679 /* Pods-BabyHouse-BabyHouseUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = BabyHouseUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = sub91.BabyHouseUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = BabyHouse;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F3BE4F3227EB489D00E3328A /* Build configuration list for PBXProject "BabyHouse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3BE4F6227EB48A200E3328A /* Debug */,
				F3BE4F6327EB48A200E3328A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3BE4F6427EB48A200E3328A /* Build configuration list for PBXNativeTarget "BabyHouse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3BE4F6527EB48A200E3328A /* Debug */,
				F3BE4F6627EB48A200E3328A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3BE4F6727EB48A200E3328A /* Build configuration list for PBXNativeTarget "BabyHouseTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3BE4F6827EB48A200E3328A /* Debug */,
				F3BE4F6927EB48A200E3328A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3BE4F6A27EB48A200E3328A /* Build configuration list for PBXNativeTarget "BabyHouseUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3BE4F6B27EB48A200E3328A /* Debug */,
				F3BE4F6C27EB48A200E3328A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		1865DB772C7A351B00C7C9D7 /* XCLocalSwiftPackageReference "InfiniteCarousel-master" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "InfiniteCarousel-master";
		};
		1865DB7F2C7A4CD600C7C9D7 /* XCLocalSwiftPackageReference "SwiftUI-Infinite-Carousel-main" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "SwiftUI-Infinite-Carousel-main";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		1849E13B2AB8491300751BE3 /* XCRemoteSwiftPackageReference "Siren" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ArtSabintsev/Siren.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.0.0;
			};
		};
		18AD6D962ACC13F100A2903B /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 7.0.0;
			};
		};
		18AD6D9B2ACC179400A2903B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 10.15.0;
			};
		};
		18BA51472AAAFA88002FEC90 /* XCRemoteSwiftPackageReference "WrappingHStack" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/dkk/WrappingHStack.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
		18CC10F42AA33BA800C2C1E3 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
		18D2F5132AC411A300F6BECD /* XCRemoteSwiftPackageReference "SwiftyJSON" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyJSON/SwiftyJSON.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.1;
			};
		};
		18D8E65D2AA5D43900189C95 /* XCRemoteSwiftPackageReference "RichText" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/NuPlay/RichText.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		182521712AEBC5C000402770 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18AD6D9B2ACC179400A2903B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		1849E13C2AB8491300751BE3 /* Siren */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1849E13B2AB8491300751BE3 /* XCRemoteSwiftPackageReference "Siren" */;
			productName = Siren;
		};
		1865DB782C7A351B00C7C9D7 /* InfiniteCarousel */ = {
			isa = XCSwiftPackageProductDependency;
			productName = InfiniteCarousel;
		};
		1865DB802C7A4CD600C7C9D7 /* SwiftUIInfiniteCarousel */ = {
			isa = XCSwiftPackageProductDependency;
			productName = SwiftUIInfiniteCarousel;
		};
		18AD6D972ACC13F100A2903B /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18AD6D962ACC13F100A2903B /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		18AD6D992ACC13F100A2903B /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18AD6D962ACC13F100A2903B /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		18AD6D9C2ACC179500A2903B /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18AD6D9B2ACC179400A2903B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		18BA51482AAAFA88002FEC90 /* WrappingHStack */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18BA51472AAAFA88002FEC90 /* XCRemoteSwiftPackageReference "WrappingHStack" */;
			productName = WrappingHStack;
		};
		18CC10F52AA33BA800C2C1E3 /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18CC10F42AA33BA800C2C1E3 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		18D2F5142AC411A300F6BECD /* SwiftyJSON */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18D2F5132AC411A300F6BECD /* XCRemoteSwiftPackageReference "SwiftyJSON" */;
			productName = SwiftyJSON;
		};
		18D8E65E2AA5D43900189C95 /* RichText */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18D8E65D2AA5D43900189C95 /* XCRemoteSwiftPackageReference "RichText" */;
			productName = RichText;
		};
/* End XCSwiftPackageProductDependency section */

/* Begin XCVersionGroup section */
		F3BE4F4327EB489D00E3328A /* BabyHouse.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				F3BE4F4427EB489D00E3328A /* BabyHouse.xcdatamodel */,
			);
			currentVersion = F3BE4F4427EB489D00E3328A /* BabyHouse.xcdatamodel */;
			path = BabyHouse.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = F3BE4F2F27EB489D00E3328A /* Project object */;
}
